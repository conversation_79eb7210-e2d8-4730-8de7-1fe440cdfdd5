import os
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any, Union
from fastapi import HTTPException
from ib_async import IB
from dotenv import load_dotenv
import asyncio
import nest_asyncio
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

try:
    nest_asyncio.apply()
except ValueError as e:
    print(f"nest_asyncio.apply() skipped: {e}")

# Configure logging
logger = logging.getLogger('ibkr-service')

load_dotenv(override=True)

class IBKRConnectionError(Exception):
    """Exception raised for IBKR connection issues"""
    pass

class IBKRTimeoutError(Exception):
    """Exception raised for IBKR timeout issues"""
    pass

class IBKRDataError(Exception):
    """Exception raised for IBKR data issues"""
    pass

class IBKRService:
    def __init__(self):
        self.ib = IB()
        self.host = os.getenv("IBKR_HOST", "127.0.0.1")
        raw_port = os.getenv("IBKR_PORT", "7496").split()[0]
        self.port = int(raw_port)
        self.client_id = int(os.getenv("IBKR_CLIENT_ID", "0"))
        self.connected = False
        self.connection_time = None
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        self.reconnect_delay = 5  # seconds
        self.last_error = None
        self.readonly = True

    async def initialize(self):
        """Initialize the service without connecting to TWS"""
        logger.info("Initializing IBKR service")
        # Reset connection state
        self.connection_attempts = 0
        self.last_error = None
        return True

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type(IBKRConnectionError)
    )
    async def connect(self, force: bool = False):
        """
        Connect to IBKR TWS or Gateway with retry logic

        Args:
            force: If True, force reconnection even if already connected
        """
        # If already connected and not forcing reconnection, return
        if self.connected and not force:
            logger.debug("Already connected to IBKR")
            return True

        # If forcing reconnection, disconnect first
        if self.connected and force:
            await self.disconnect()

        self.connection_attempts += 1

        try:
            logger.info(f"Connecting to IBKR at {self.host}:{self.port} (attempt {self.connection_attempts})")
            await self.ib.connectAsync(
                host=self.host,
                port=self.port,
                clientId=self.client_id,
                readonly=self.readonly,
                timeout=20
            )

            # Verify connection is working by making a simple request
            await self.ib.reqCurrentTimeAsync()

            self.connected = True
            self.connection_time = datetime.now()
            self.connection_attempts = 0  # Reset counter on success
            logger.info(f"Connected to IBKR at {self.host}:{self.port}")
            return True

        except asyncio.TimeoutError as e:
            self.connected = False
            self.last_error = str(e)
            logger.error(f"Timeout connecting to IBKR: {str(e)}")
            raise IBKRTimeoutError(f"Timeout connecting to IBKR: {str(e)}")

        except Exception as e:
            self.connected = False
            self.last_error = str(e)
            logger.error(f"Failed to connect to IBKR: {str(e)}")

            # Check if TWS is not running or not connected to IB servers
            if "Not connected" in str(e) or "Connection refused" in str(e):
                raise IBKRConnectionError(f"TWS/Gateway not running or not accessible: {str(e)}")
            else:
                raise IBKRConnectionError(f"Failed to connect to IBKR: {str(e)}")

    async def disconnect(self):
        """Disconnect from IBKR asynchronously"""
        if self.connected:
            logger.info("Disconnecting from IBKR")
            # Use asyncio.to_thread for potentially blocking operations
            await asyncio.to_thread(self.ib.disconnect)
            self.connected = False
            logger.info("Disconnected from IBKR")
            return True
        return False

    async def check_connection(self):
        """Check if connection is still valid and reconnect if needed"""
        if not self.connected:
            logger.info("Not connected to IBKR, attempting to connect")
            return await self.connect()

        try:
            # Try a simple request to verify connection
            await self.ib.reqCurrentTimeAsync()
            logger.debug("Connection to IBKR is valid")
            return True
        except Exception as e:
            logger.warning(f"Connection check failed: {str(e)}, reconnecting...")
            self.connected = False
            return await self.connect(force=True)

    async def get_portfolio(self) -> List[Dict]:
        """Get portfolio positions with connection management"""
        try:
            await self.check_connection()

            logger.info("Fetching portfolio positions")
            positions = await asyncio.to_thread(self.ib.positions)

            # Format portfolio data
            portfolio_data = []
            for pos in positions:
                position_data = {
                    'symbol': pos.contract.symbol,
                    'secType': pos.contract.secType,
                    'exchange': pos.contract.exchange,
                    'currency': pos.contract.currency,
                    'position': pos.position,
                    'avgCost': pos.avgCost,
                    'marketPrice': getattr(pos, 'marketPrice', 0),
                    'marketValue': getattr(pos, 'marketValue', 0),
                    'unrealizedPNL': getattr(pos, 'unrealizedPNL', 0),
                    'realizedPNL': getattr(pos, 'realizedPNL', 0)
                }
                portfolio_data.append(position_data)

            logger.info(f"Retrieved {len(portfolio_data)} portfolio positions")
            return portfolio_data

        except Exception as e:
            logger.error(f"Failed to get portfolio: {str(e)}")
            raise IBKRDataError(f"Failed to get portfolio: {str(e)}")

    async def get_account_summary(self, tags: List[str] = None) -> Dict:
        """Get account summary with connection management"""
        try:
            await self.check_connection()

            if tags is None:
                tags = ["NetLiquidation", "TotalCashValue", "SettledCash",
                        "AccruedCash", "BuyingPower", "EquityWithLoanValue",
                        "PreviousDayEquityWithLoanValue", "GrossPositionValue",
                        "RegTMargin", "InitMarginReq", "MaintMarginReq",
                        "AvailableFunds", "ExcessLiquidity", "Cushion",
                        "FullInitMarginReq", "FullMaintMarginReq", "FullAvailableFunds",
                        "FullExcessLiquidity", "LookAheadNextChange",
                        "LookAheadInitMarginReq", "LookAheadMaintMarginReq",
                        "LookAheadAvailableFunds", "LookAheadExcessLiquidity",
                        "HighestSeverity", "DayTradesRemaining", "Leverage"]

            logger.info("Fetching account summary")
            account_summary = await self.ib.accountSummaryAsync()

            # Convert to dictionary format
            summary_dict = {}
            for item in account_summary:
                summary_dict[item.tag] = {
                    'value': item.value,
                    'currency': item.currency
                }

            logger.info("Retrieved account summary")
            return summary_dict

        except Exception as e:
            logger.error(f"Failed to get account summary: {str(e)}")
            raise IBKRDataError(f"Failed to get account summary: {str(e)}")

    async def fetch_portfolio_details(self) -> Dict:
        """Fetch portfolio details from IBKR (legacy method)"""
        try:
            portfolio_data = await self.get_portfolio()
            account_summary = await self.get_account_summary()

            return {
                'positions': portfolio_data,
                'account_summary': account_summary
            }

        except Exception as e:
            logger.error(f"Failed to fetch portfolio details: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to fetch portfolio: {str(e)}"
            )

# Create a singleton instance
ibkr_service = IBKRService()