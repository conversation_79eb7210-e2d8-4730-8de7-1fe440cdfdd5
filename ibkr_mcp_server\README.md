# IBKR MCP Server

A Model Context Protocol (MCP) server that provides access to Interactive Brokers' TWS API, allowing <PERSON> and other LLM applications to interact with your IBKR trading account.

## Overview

This server implements the Model Context Protocol to provide <PERSON> with access to:
- Portfolio positions and account information
- Market data for various securities
- Trading capabilities (placing and managing orders)
- Historical data and charting

## System Requirements

- Python 3.9 or higher
- Interactive Brokers TWS or IB Gateway (running and logged in)
- <PERSON> (for direct integration)

## Installation

### Step 1: Clone the repository

```bash
git clone https://github.com/your_username/ibkr-mcp-server.git
cd ibkr-mcp-server
```

### Step 2: Set up a Python virtual environment

Using Anaconda (recommended):
```bash
conda create -n ibkr_mcp_env python=3.9
conda activate ibkr_mcp_env
```

Or using standard Python:
```bash
python -m venv ibkr_mcp_env
source ibkr_mcp_env/bin/activate  # On Windows: ibkr_mcp_env\Scripts\activate
```

### Step 3: Install the TWS API library

First, ensure you have the TWS API repository:
```bash
cd ibkr_mcp_server/source/pythonclient
```

Build and install the TWS API:
```bash
python setup.py sdist
python setup.py bdist_wheel
python -m pip install --user --upgrade dist/ibapi-9.81.1-py3-none-any.whl
```

### Step 4: Install other dependencies

Return to the main project directory and install the required packages:
```bash
cd ../../
pip install -r requirements.txt
```

## Configuration

Create a `.env` file in the project root with your IBKR connection settings:

```
IBKR_HOST=127.0.0.1
IBKR_PORT=7497  # Use 7496 for TWS, 7497 for IB Gateway
IBKR_CLIENT_ID=0
```

## Running the Server

### Option 1: Standalone Mode

Run the server directly:

```bash
python mcp_server_main.py
```

### Option 2: Claude Desktop Integration

1. Open Claude Desktop and go to Settings (from the Claude menu, not the in-app settings)
2. Add the following configuration to your `claude_desktop_config.json`:

```json
{
  "name": "IBKR Trading Assistant",
  "description": "Interactive Brokers trading assistant for Claude Desktop",
  "version": "1.0.0",
  "servers": [
    {
      "name": "IBKR MCP Server",
      "description": "Provides access to Interactive Brokers TWS API",
      "transport": {
        "type": "stdio",
        "command": "python",
        "args": ["path/to/ib-tws/mcp_server_main.py"],
        "env": {
          "IBKR_HOST": "127.0.0.1",
          "IBKR_PORT": "7497",
          "IBKR_CLIENT_ID": "0"
        }
      }
    }
  ]
}
```

3. Restart Claude Desktop

## Usage

Once connected to Claude Desktop, you can interact with your IBKR account by:

1. Starting TWS or IB Gateway and logging in
2. Using the `connect_to_tws` tool in Claude to establish a connection
3. Accessing your portfolio, account information, and market data
4. Placing trades and managing orders

Example prompts:
- "Connect to my Interactive Brokers account"
- "Show me my current portfolio positions"
- "Get the current market price for AAPL"
- "Place a limit order to buy 10 shares of MSFT at $300"

## Available Resources

- `ibkr://portfolio`: All portfolio positions
- `ibkr://portfolio/{symbol}`: Specific portfolio position
- `ibkr://account-summary`: Complete account summary
- `ibkr://account-summary/{tag}`: Specific account value
- `ibkr://market-data/{symbol}`: Market data for a specific symbol
- `docs://overview`: Documentation overview

## Troubleshooting

- Ensure TWS or IB Gateway is running and logged in
- Check that the API connection is enabled in TWS/IB Gateway settings
- Verify the port numbers match between your configuration and TWS/IB Gateway
- Check the log file at `ibkr_mcp_server.log` for detailed error information

## License

This project is subject to the terms of the IB API Non-Commercial License or the IB API Commercial License, as applicable.
