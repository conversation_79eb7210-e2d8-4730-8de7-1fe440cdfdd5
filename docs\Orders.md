# Orders

## Available Orders

### Basic Orders

#### Auction

An [Auction](https://www.interactivebrokers.com/en/index.php?f=578) order is entered into the electronic trading system during the pre-market opening period for execution at the Calculated Opening Price (COP). If your order is not filled on the open, the order is re-submitted as a limit order with the limit price set to the COP or the best bid/ask after the market opens.

- Products: FUT, STK

```
order = Order()
order.action = action
order.tif = "AUC"
order.orderType = "MTL"
order.totalQuantity = quantity
order.lmtPrice = price
```

#### Discretionary

A Discretionary order is a limit order submitted with a hidden, specified 'discretionary' amount off the limit price, which may be used to increase the price range over which the limit order is eligible to execute. The market sees only the limit price.

- Products: STK

```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity
order.lmtPrice = price
order.discretionaryAmt = discretionaryAmount
```

#### Market

A [Market](https://www.interactivebrokers.com/en/index.php?f=602) order is an order to buy or sell at the market bid or offer price. A market order may increase the likelihood of a fill and the speed of execution, but unlike the Limit order a Market order provides no price protection and may fill at a price far lower/higher than the current displayed bid/ask.

- Products: BOND, CFD, EFP, CASH, FUND, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "MKT"
order.totalQuantity = quantity
```

#### Market If Touched

A [Market If Touched](https://www.interactivebrokers.com/en/index.php?f=600) (MIT) is an order to buy (or sell) a contract below (or above) the market. Its purpose is to take advantage of sudden or unexpected changes in share or other prices and provides investors with a trigger price to set an order in motion. Investors may be waiting for excessive strength (or weakness) to cease, which might be represented by a specific price point. MIT orders can be used to determine whether or not to enter the market once a specific price level has been achieved. This order is held in the system until the trigger price is touched, and is then submitted as a market order. An MIT order is similar to a stop order, except that an MIT sell order is placed above the current market price, and a stop sell order is placed below

- Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "MIT"
order.totalQuantity = quantity
order.auxPrice = price
```

#### Market On Close

A [Market On Close](https://www.interactivebrokers.com/en/index.php?f=599) (MOC) order is a market order that is submitted to execute as close to the closing price as possible.

- Products: CFD, FUT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "MOC"
order.totalQuantity = quantity
```

#### Market On Open

A [Market On Open](https://www.interactivebrokers.com/en/index.php?f=598) (MOO) combines a market order with the OPG time in force to create an order that is automatically submitted at the market's open and fills at the market price.

- Products: CFD, FUT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "MKT"
order.totalQuantity = quantity
order.tif = "OPG"
```

#### Pegged to Market

A [pegged-to-market order](https://www.interactivebrokers.com/en/index.php?f=616) is designed to maintain a purchase price relative to the national best offer (NBO) or a sale price relative to the national best bid (NBB). Depending on the width of the quote, this order may be passive or aggressive. The trader creates the order by entering a limit price which defines the worst limit price that they are willing to accept. Next, the trader enters an offset amount which computes the active limit price as follows: Sell order price = Bid price + offset amount Buy order price = Ask price - offset amount

- Products: STK

```
order = Order()
order.action = action
order.orderType = "PEG MKT"
order.totalQuantity = quantity
order.auxPrice = marketOffset#Offset price
```

#### Pegged to Stock

A [Pegged to Stock](https://www.interactivebrokers.com/en/index.php?f=615) order continually adjusts the option order price by the product of a signed user-define delta and the change of the option's underlying stock price. The delta is entered as an absolute and assumed to be positive for calls and negative for puts. A buy or sell call order price is determined by adding the delta times a change in an underlying stock price to a specified starting price for the call. To determine the change in price, the stock reference price is subtracted from the current NBBO midpoint. The Stock Reference Price can be defined by the user, or defaults to the NBBO midpoint at the time of the order if no reference price is entered. You may also enter a high/low stock price range which cancels the order when reached. The delta times the change in stock price will be rounded to the nearest penny in favor of the order.

- Products: OPT

```
order = Order()
order.action = action
order.orderType = "PEG STK"
order.totalQuantity = quantity
order.delta = delta
order.stockRefPrice = stockReferencePrice
order.startingPrice = startingPrice
```

#### Pegged to Primary

[Relative](https://www.interactivebrokers.com/en/index.php?f=613) (a.k.a. Pegged-to-Primary) orders provide a means for traders to seek a more aggressive price than the National Best Bid and Offer (NBBO). By acting as liquidity providers, and placing more aggressive bids and offers than the current best bids and offers, traders increase their odds of filling their order. Quotes are automatically adjusted as the markets move, to remain aggressive. For a buy order, your bid is pegged to the NBB by a more aggressive offset, and if the NBB moves up, your bid will also move up. If the NBB moves down, there will be no adjustment because your bid will become even more aggressive and execute. For sales, your offer is pegged to the NBO by a more aggressive offset, and if the NBO moves down, your offer will also move down. If the NBO moves up, there will be no adjustment because your offer will become more aggressive and execute. In addition to the offset, you can define an absolute cap, which works like a limit price, and will prevent your order from being executed above or below a specified level. Stocks, Options and Futures - not available on paper trading

- Products: CFD, STK, OPT, FUT

```
order = Order()
order.action = action
order.orderType = "REL"
order.totalQuantity = quantity
order.lmtPrice = priceCap
order.auxPrice = offsetAmount
```

#### Sweep to Fill

[Sweep-to-fill](https://www.interactivebrokers.com/en/index.php?f=607) orders are useful when a trader values speed of execution over price. A sweep-to-fill order identifies the best price and the exact quantity offered/available at that price, and transmits the corresponding portion of your order for immediate execution. Simultaneously it identifies the next best price and quantity offered/available, and submits the matching quantity of your order for immediate execution.

- Products: CFD, STK, WAR (SMART only)

```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity
order.lmtPrice = price
order.sweepToFill = True
```

#### Auction Limit

For option orders routed to the Boston Options Exchange (BOX) you may elect to participate in the BOX's price improvement auction in pennies. All BOX-directed [price improvement](https://www.interactivebrokers.com/en/index.php?f=614) orders are immediately sent from Interactive Brokers to the BOX order book, and when the terms allow, IB will evaluate it for inclusion in a price improvement auction based on price and volume priority. In the auction, your order will have priority over broker-dealer price improvement orders at the same price. An Auction Limit order at a specified price. Use of a limit order ensures that you will not receive an execution at a price less favorable than the limit price. Enter limit orders in penny increments with your auction improvement amount computed as the difference between your limit order price and the nearest listed increment.

- Products: OPT (BOX only)

```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity
order.lmtPrice = price
order.auctionStrategy = auctionStrategy
```

#### Auction Pegged to Stock

For option orders routed to the Boston Options Exchange (BOX) you may elect to participate in the BOX's price improvement auction in pennies. All BOX-directed [price improvement](https://www.interactivebrokers.com/en/index.php?f=614) orders are immediately sent from Interactive Brokers to the BOX order book, and when the terms allow, IB will evaluate it for inclusion in a price improvement auction based on price and volume priority. In the auction, your order will have priority over broker-dealer price improvement orders at the same price. An Auction Pegged to Stock order adjusts the order price by the product of a signed delta (which is entered as an absolute and assumed to be positive for calls, negative for puts) and the change of the option's underlying stock price. A buy or sell call order price is determined by adding the delta times a change in an underlying stock price change to a specified starting price for the call. To determine the change in price, a stock reference price (NBBO midpoint at the time of the order is assumed if no reference price is entered) is subtracted from the current NBBO midpoint. A stock range may also be entered that cancels an order when reached. The delta times the change in stock price will be rounded to the nearest penny in favor of the order and will be used as your auction improvement amount.

- Products: OPT (BOX only)

```
order = Order()
order.action = action
order.orderType = "PEG STK"
order.totalQuantity = quantity
order.delta = delta
order.startingPrice = startingPrice
```

#### Auction Relative

For option orders routed to the Boston Options Exchange (BOX) you may elect to participate in the BOX's price improvement auction in pennies. All BOX-directed [price improvement](https://www.interactivebrokers.com/en/index.php?f=614) orders are immediately sent from Interactive Brokers to the BOX order book, and when the terms allow, IB will evaluate it for inclusion in a price improvement auction based on price and volume priority. In the auction, your order will have priority over broker-dealer price improvement orders at the same price. An Auction Relative order that adjusts the order price by the product of a signed delta (which is entered as an absolute and assumed to be positive for calls, negative for puts) and the change of the option's underlying stock price. A buy or sell call order price is determined by adding the delta times a change in the underlying stock price to a specified starting price for the call. To determine the price change, a stock reference price (NBBO midpoint when the order is assumed if no reference price is entered) is subtracted from the current NBBO midpoint. A stock range may also be entered that cancels an order when reached. The delta times the change in stock price will be rounded to the nearest penny in favor of the order and will be used as your auction improvement amount.

- Products: OPT (BOX only)

```
order = Order()
order.action = action
order.orderType = "REL"
order.totalQuantity = quantity
order.auxPrice = offset
```

#### Block

The [Block](https://www.interactivebrokers.com/en/index.php?f=580) attribute is used for large volume option orders on ISE that consist of at least 50 contracts. To execute large-volume orders over time without moving the market, use the [Accumulate/Distribute](https://interactivebrokers.github.io/tws-api/ibalgos.html#ad) algorithm.

- Products: OPT

```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity#Large volumes!
order.lmtPrice = price
order.blockOrder = True
```

#### Box Top

A [Box Top](https://www.interactivebrokers.com/en/index.php?f=582) order executes as a market order at the current best price. If the order is only partially filled, the remainder is submitted as a limit order with the limit price equal to the price at which the filled portion of the order executed.

- Products: OPT (BOX only)

```
order = Order()
order.action = action
order.orderType = "BOX TOP"
order.totalQuantity = quantity
```

#### Limit Order

A [Limit order](https://www.interactivebrokers.com/en/index.php?f=593) is an order to buy or sell at a specified price or better. The Limit order ensures that if the order fills, it will not fill at a price less favorable than your limit price, but it does not guarantee a fill.

- Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR


```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity
order.lmtPrice = limitPrice
```

#### Forex Cash Quantity Order

Forex orders can be placed in denomination of second currency in pair using cashQty field.
Requires TWS or IBG 963+

- Products: CASH

```
order = Order()
order.action = action
order.orderType = "LMT"
order.lmtPrice = limitPrice
order.cashQty = cashQty
```

#### Limit if Touched

A [Limit if Touched](https://www.interactivebrokers.com/en/index.php?f=592) is an order to buy (or sell) a contract at a specified price or better, below (or above) the market. This order is held in the system until the trigger price is touched. An LIT order is similar to a stop limit order, except that an LIT sell order is placed above the current market price, and a stop limit sell order is placed below.

- Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "LIT"
order.totalQuantity = quantity
order.lmtPrice = limitPrice
order.auxPrice = triggerPrice
```

#### Limit on Close

A [Limit-on-close](https://www.interactivebrokers.com/en/index.php?f=591) (LOC) order will be submitted at the close and will execute if the closing price is at or better than the submitted limit price.

- Products: CFD, FUT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "LOC"
order.totalQuantity = quantity
order.lmtPrice = limitPrice
```

#### Limit on Open

A [Limit-on-Open](https://www.interactivebrokers.com/en/index.php?f=590) (LOO) order combines a limit order with the OPG time in force to create an order that is submitted at the market's open, and that will only execute at the specified limit price or better. Orders are filled in accordance with specific exchange rules.

- Products: CFD, STK, OPT, WAR

```
order = Order()
order.action = action
order.tif = "OPG"
order.orderType = "LMT"
order.totalQuantity = quantity
order.lmtPrice = limitPrice
```

#### Passive Relative

[Passive Relative](https://www.interactivebrokers.com/en/index.php?f=3124) orders provide a means for traders to seek a less aggressive price than the National Best Bid and Offer (NBBO) while keeping the order pegged to the best bid (for a buy) or ask (for a sell). The order price is automatically adjusted as the markets move to keep the order less aggressive. For a buy order, your order price is pegged to the NBB by a less aggressive offset, and if the NBB moves up, your bid will also move up. If the NBB moves down, there will be no adjustment because your bid will become aggressive and execute. For a sell order, your price is pegged to the NBO by a less aggressive offset, and if the NBO moves down, your offer will also move down. If the NBO moves up, there will be no adjustment because your offer will become aggressive and execute. In addition to the offset, you can define an absolute cap, which works like a limit price, and will prevent your order from being executed above or below a specified level. The Passive Relative order is similar to the Relative/Pegged-to-Primary order, except that the Passive relative subtracts the offset from the bid and the Relative adds the offset to the bid.

- Products: STK, WAR

```
order = Order()
order.action = action
order.orderType = "PASSV REL"
order.totalQuantity = quantity
order.auxPrice = offset
```

#### Pegged to Midpoint

A [pegged-to-midpoint](https://www.interactivebrokers.com/en/index.php?f=1058) order provides a means for traders to seek a price at the midpoint of the National Best Bid and Offer (NBBO). The price automatically adjusts to peg the midpoint as the markets move, to remain aggressive. For a buy order, your bid is pegged to the NBBO midpoint and the order price adjusts automatically to continue to peg the midpoint if the market moves. The price only adjusts to be more aggressive. If the market moves in the opposite direction, the order will execute.

- Products: STK

```
order = Order()
order.action = action
order.orderType = "PEG MID"
order.totalQuantity = quantity
order.auxPrice = offset
order.lmtPrice = limitPrice
```

#### Market to Limit

A [Market-to-Limit](https://www.interactivebrokers.com/en/index.php?f=597) (MTL) order is submitted as a market order to execute at the current best market price. If the order is only partially filled, the remainder of the order is canceled and re-submitted as a limit order with the limit price equal to the price at which the filled portion of the order executed.

- Products: CFD, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "MTL"
order.totalQuantity = quantity
```

#### Market with Protection

This order type is useful for futures traders using Globex. A [Market with Protection](https://www.interactivebrokers.com/en/index.php?f=601) order is a market order that will be cancelled and resubmitted as a limit order if the entire order does not immediately execute at the market price. The limit price is set by Globex to be close to the current market price, slightly higher for a sell order and lower for a buy order.

- Products: FUT, FOP

```
order = Order()
order.action = action
order.orderType = "MKT PRT"
order.totalQuantity = quantity
```

#### Stop

A [Stop](https://www.interactivebrokers.com/en/index.php?f=609) order is an instruction to submit a buy or sell market order if and when the user-specified stop trigger price is attained or penetrated. A Stop order is not guaranteed a specific execution price and may execute significantly away from its stop price. A Sell Stop order is always placed below the current market price and is typically used to limit a loss or protect a profit on a long stock position. A Buy Stop order is always placed above the current market price. It is typically used to limit a loss or help protect a profit on a short sale.

- Products: CFD, BAG, CASH, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "STP"
order.auxPrice = stopPrice
order.totalQuantity = quantity
```

#### Stop Limit

A [Stop-Limit](https://www.interactivebrokers.com/en/index.php?f=608) order is an instruction to submit a buy or sell limit order when the user-specified stop trigger price is attained or penetrated. The order has two basic components: the stop price and the limit price. When a trade has occurred at or through the stop price, the order becomes executable and enters the market as a limit order, which is an order to buy or sell at a specified price or better.

- Products: CFD, CASH, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "STP LMT"
order.totalQuantity = quantity
order.lmtPrice = limitPrice
order.auxPrice = stopPrice
```

#### Stop with Protection

A [Stop with Protection](https://www.interactivebrokers.com/en/index.php?f=3077) order combines the functionality of a stop limit order with a market with protection order. The order is set to trigger at a specified stop price. When the stop price is penetrated, the order is triggered as a market with protection order, which means that it will fill within a specified protected price range equal to the trigger price +/- the exchange-defined protection point range. Any portion of the order that does not fill within this protected range is submitted as a limit order at the exchange-defined trigger price +/- the protection points.

- Products: FUT

```
order = Order()
order.totalQuantity = quantity
order.action = action
order.orderType = "STP PRT"
order.auxPrice = stopPrice
```

#### Trailing Stop

A sell [trailing stop](https://www.interactivebrokers.com/en/index.php?f=605) order sets the stop price at a fixed amount below the market price with an attached "trailing" amount. As the market price rises, the stop price rises by the trail amount, but if the stock price falls, the stop loss price doesn't change, and a market order is submitted when the stop price is hit. This technique is designed to allow an investor to specify a limit on the maximum possible loss, without setting a limit on the maximum possible gain. "Buy" trailing stop orders are the mirror image of sell trailing stop orders, and are most appropriate for use in falling markets.

Note that Trailing Stop orders can have the trailing amount specified as a percent, as in the example below, or as an absolute amount which is specified in the auxPrice field.

- Products: CFD, CASH, FOP, FUT, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "TRAIL"
order.totalQuantity = quantity
order.trailingPercent = trailingPercent
order.trailStopPrice = trailStopPrice
```

#### Trailing Stop Limit

A [trailing stop limit](https://www.interactivebrokers.com/en/index.php?f=606) order is designed to allow an investor to specify a limit on the maximum possible loss, without setting a limit on the maximum possible gain. A SELL trailing stop limit moves with the market price, and continually recalculates the stop trigger price at a fixed amount below the market price, based on the user-defined "trailing" amount. The limit order price is also continually recalculated based on the limit offset. As the market price rises, both the stop price and the limit price rise by the trail amount and limit offset, respectively, but if the stock price falls, the stop price remains unchanged, and when the stop price is hit, a limit order is submitted at the last calculated limit price. A "Buy" trailing stop limit order is the mirror image of a sell trailing stop limit order and is generally used in falling markets.

Trailing Stop Limit orders can be sent with the trailing amount specified as an absolute amount, as in the example below, or as a percentage, specified in the trailingPercent field.

**Important:** The 'limit offset' field is set by default in the TWS/IBG settings in v963+. This setting either needs to be changed in the Order Presets, the default value accepted, or the limit price offset sent from the API, as in the example below. Neither the 'limit price' nor the 'limit price offset' fields can be set in TRAIL LIMIT orders.

- Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR

```
order = Order()
order.action = action
order.orderType = "TRAIL LIMIT"
order.totalQuantity = quantity
order.trailStopPrice = trailStopPrice
order.lmtPriceOffset = lmtPriceOffset
order.auxPrice = trailingAmount
```

#### Combo Limit

Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure best execution.

- Products: OPT, STK, FUT

```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity
order.lmtPrice = limitPrice
if nonGuaranteed:

order.smartComboRoutingParams = []
order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))

```

#### Combo Market

Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure best execution.

- Products: OPT, STK, FUT

```
order = Order()
order.action = action
order.orderType = "MKT"
order.totalQuantity = quantity
if nonGuaranteed:

     order.smartComboRoutingParams = []
     order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1")) 
```

#### Combo Limit with Price per Leg

Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure best execution.

- Products: OPT, STK, FUT

```
order = Order()
order.action = action
order.orderType = "LMT"
order.totalQuantity = quantity
order.orderComboLegs = []
for price in legPrices:

     comboLeg = OrderComboLeg()
     comboLeg.price = price
     order.orderComboLegs.append(comboLeg)

if nonGuaranteed:
    order.smartComboRoutingParams = []
    order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
        
```

#### Relative Limit Combo

Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure best execution.

- Products: OPT, STK, FUT

```
order = Order()
order.action = action
order.totalQuantity = quantity
order.orderType = "REL + LMT"
order.lmtPrice = limitPrice
if nonGuaranteed:

    order.smartComboRoutingParams = []
    order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
```

#### Relative Market Combo

Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure best execution.

- Products: OPT, STK, FUT

```
order = Order()
order.action = action
order.totalQuantity = quantity
order.orderType = "REL + MKT"
if nonGuaranteed: 

    order.smartComboRoutingParams = []
    order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))

```

#### Volatility

Specific to US options, investors are able to create and enter [Volatility-type](https://www.interactivebrokers.com/en/index.php?f=604) orders for options and combinations rather than price orders. Option traders may wish to trade and position for movements in the price of the option determined by its implied volatility. Because implied volatility is a key determinant of the premium on an option, traders position in specific contract months in an effort to take advantage of perceived changes in implied volatility arising before, during or after earnings or when company specific or broad market volatility is predicted to change. In order to create a Volatility order, clients must first create a Volatility Trader page from the Trading Tools menu and as they enter option contracts, premiums will display in percentage terms rather than premium. The buy/sell process is the same as for regular orders priced in premium terms except that the client can limit the volatility level they are willing to pay or receive.

- Products: FOP, OPT

```
order = Order()
order.action = action
order.orderType = "VOL"
order.totalQuantity = quantity
order.volatility = volatilityPercent#Expressed in percentage (40%)
order.volatilityType = volatilityType# 1=daily, 2=annual
```

#### Pegged to Benchmark

The [Pegged to Benchmark ](https://www.interactivebrokers.com/en/index.php?f=7100)order is similar to the Pegged to Stock order for options, except that the Pegged to Benchmark allows you to specify any asset type as the reference (benchmark) contract for a stock or option order. Both the primary and reference contracts must use the same currency.

- Products: STK, OPT

```
order = Order()
order.orderType = "PEG BENCH"
#BUY or SELL
order.action = action
order.totalQuantity = quantity
#Beginning with price...
order.startingPrice = startingPrice
#increase/decrease price..
order.isPeggedChangeAmountDecrease = peggedChangeAmountDecrease
#by... (and likewise for price moving in opposite direction)
order.peggedChangeAmount = peggedChangeAmount
#whenever there is a price change of...
order.referenceChangeAmount = referenceChangeAmount
#in the reference contract...
order.referenceContractId = referenceConId
#being traded at...
order.referenceExchange = referenceExchange
#starting reference price is...
order.stockRefPrice = stockReferencePrice
#Keep order active as long as reference contract trades between...
order.stockRangeLower = referenceContractLowerRange
#and...
order.stockRangeUpper = referenceContractUpperRange
```

#### Limit Order With Manual Order Time

The Limit Order With Manual Order Time is a [Limit Order](https://interactivebrokers.github.io/tws-api/basic_orders.html#limitorder) with ManualOrderTime property.

```
order = OrderSamples.LimitOrder(action, quantity, limitPrice)
order.manualOrderTime = manualOrderTime
```

**Placing a Limit Order With Manual Order Time**
The *Manual Order Time* field is required for, and becomes editable for, "manual" orders, which are orders that originate from a client (whether by phone, email, chat, verbally from the floor, from another desk, etc.) and are then entered into the system.
The *Manual Order Time* field is not used for client orders received electronically, nor for orders that originate from the account manager (with discretion) in the client's accounts, or when an order is allocated to ALL accounts or among multiple accounts using an account group or model portfolio.

```
self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), OrderSamples.LimitOrderWithManualOrderTime("BUY", Decimal("100"), 111.11, "********-13:00:00"))
```

**Canceling a Limit Order With Manual Order Time**
The *Manual Order Cancel Time* field is required for, and becomes editable for, "manual" order cancelations, which are order cancelations that originate from a client (whether by phone, email, chat, verbally from the floor, from another desk, etc.) and are then entered into the system.
The *Manual Order Cancel Time* field is not used for client orders cancelations received electronically, nor for orders that originate from the account manager (with discretion) in the client's accounts, or when an order is allocated to ALL accounts or among multiple accounts using an account group or model portfolio.

```
self.cancelOrder(self.simplePlaceOid, "********-13:00:00")
```

### Advanced Orders and Algos

#### Hedging

Hedging orders are similar to Bracket Orders. With a hedging order, a child order is submitted only on execution of the parent. Orders can be hedged by an attached forex trade, Beta hedge, or Pair Trade, just as in TWS:

For an example of a forex hedge, when buying a contract on a currency other than your base, you can attach an FX order to convert base currency to the currency of the contract to cover the cost of the trade, thanks to the TWS API's [Attaching Orders](https://interactivebrokers.github.io/tws-api/order_submission.html#order_attach) mechanism.

```
@staticmethod
def MarketFHedge(parentOrderId:int, action:str):

    #FX Hedge orders can only have a quantity of 0
		order = OrderSamples.MarketOrder(action, 0)
		order.parentId = parentOrderId
		order.hedgeType = "F"
		return order
...
		# Parent order on a contract which currency differs from your base currency
		parent = OrderSamples.LimitOrder("BUY", 100, 10)
		parent.orderId = self.nextOrderId()
		parent.transmit = False
		# Hedge on the currency conversion
		hedge = OrderSamples.MarketFHedge(parent.orderId, "BUY")
		Place the parent first...
		self.placeOrder(parent.orderId, ContractSamples.EuropeanStock(), parent)
		# Then the hedge order
		self.placeOrder(self.nextOrderId(), ContractSamples.EurGbpFx(), hedge)
```

Note that in some cases it will be necessary to include a small delay of 50 ms or less after placing the parent order for processing, before placing the child order. Otherwise the error "10006: Missing parent order" will be triggered.

#### **Bracket Orders**

[Bracket Orders](https://www.interactivebrokers.com/en/index.php?f=583) are designed to help limit your loss and lock in a profit by "bracketing" an order with two opposite-side orders. A BUY order is bracketed by a high-side sell limit order and a low-side sell stop order. A SELL order is bracketed by a high-side buy stop order and a low side buy limit order. Note how bracket orders make use of the TWS API's [Attaching Orders](https://interactivebrokers.github.io/tws-api/order_submission.html#order_attach) mechanism.

One key thing to keep in mind is to handle the order transmission accurately. Since a Bracket consists of three orders, there is always a risk that at least one of the orders gets filled before the entire bracket is sent. To avoid it, make use of the [IBApi.Order.Transmit](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#aaa1c4f31b9580ee0715edcd78a51cbec) flag. When this flag is set to 'false', the TWS will receive the order but it will not send (transmit) it to the servers. In the example below, the first (parent) and second (takeProfit) orders will be send to the TWS but not transmitted to the servers. When the last child order (stopLoss) is sent however and given that its [IBApi.Order.Transmit](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#aaa1c4f31b9580ee0715edcd78a51cbec) flag is set to true, the TWS will interpret this as a signal to transmit not only its parent order but also the rest of siblings, removing the risks of an accidental execution.

```
@staticmethod
def BracketOrder(parentOrderId:int, action:str, quantity:Decimal, 
								 limitPrice:float, takeProfitLimitPrice:float, 
								 stopLossPrice:float):

		#This will be our main or "parent" order
		parent = Order()
		parent.orderId = parentOrderId
		parent.action = action
		parent.orderType = "LMT"
		parent.totalQuantity = quantity
		parent.lmtPrice = limitPrice
		#The parent and children orders will need this attribute set to False to prevent accidental executions.
		#The LAST CHILD will have it set to True, 
		parent.transmit = False

		takeProfit = Order()
		takeProfit.orderId = parent.orderId + 1
		takeProfit.action = "SELL" if action == "BUY" else "BUY"
		takeProfit.orderType = "LMT"
		takeProfit.totalQuantity = quantity
		takeProfit.lmtPrice = takeProfitLimitPrice
		takeProfit.parentId = parentOrderId
		takeProfit.transmit = False

		stopLoss = Order()
		stopLoss.orderId = parent.orderId + 2
		stopLoss.action = "SELL" if action == "BUY" else "BUY"
		stopLoss.orderType = "STP"
		#Stop trigger price
		stopLoss.auxPrice = stopLossPrice
		stopLoss.totalQuantity = quantity
		stopLoss.parentId = parentOrderId
		#In this case, the low side order will be the last child being sent. Therefore, it needs to set this attribute to True 
		#to activate all its predecessors
		stopLoss.transmit = True

		bracketOrder = [parent, takeProfit, stopLoss]
		return bracketOrder

...
		bracket = OrderSamples.BracketOrder(self.nextOrderId(), "BUY", 100, 30, 40, 20)
		for o in bracket:
				self.placeOrder(o.orderId, ContractSamples.EuropeanStock(), o)
				self.nextOrderId()  # need to advance this we'll skip one extra oid, it's fine
```

#### One Cancels All

The [One-Cancels All](https://www.interactivebrokers.com/en/index.php?f=617) (OCA) order type allows an investor to place multiple and possibly unrelated orders assigned to a group. The aim is to complete just one of the orders, which in turn will cause TWS to cancel the remaining orders. The investor may submit several orders aimed at taking advantage of the most desirable price within the group. Completion of one piece of the group order causes cancellation of the remaining group orders while partial completion causes the group to re-balance. An investor might desire to sell 1000 shares of only ONE of three positions held above prevailing market prices. The OCA order group allows the investor to enter prices at specified target levels and if one is completed, the other two will automatically cancel. Alternatively, an investor may wish to take a LONG position in eMini S&P stock index futures in a falling market or else SELL US treasury futures at a more favorable price. Grouping the two orders using an OCA order type offers the investor two chances to enter a similar position, while only running the risk of taking on a single position.

```
@staticmethod
def OneCancelsAll(ocaGroup:str, ocaOrders:ListOfOrder, ocaType:int):

		for o in ocaOrders:

				o.ocaGroup = ocaGroup
				o.ocaType = ocaType

		return ocaOrders

...
		ocaOrders = [OrderSamples.LimitOrder("BUY", 1, 10), OrderSamples.LimitOrder("BUY", 1, 11),
								 OrderSamples.LimitOrder("BUY", 1, 12)]
		OrderSamples.OneCancelsAll("TestOCA_" + str(self.nextValidOrderId), ocaOrders, 2)
		for o in ocaOrders:
				self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), o)
```

##### OCA Types

Via the [IBApi.Order.OcaType](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a3485999c42d64f3c879ec4a424bab697) attribute, the way in which remaining orders should be handled after an execution can be configured as indicated in the table below:

| Value | Description                                                  |
| ----- | ------------------------------------------------------------ |
| 1     | Cancel all remaining orders with block.*                     |
| 2     | Remaining orders are proportionately reduced in size with block.* |
| 3     | Remaining orders are proportionately reduced in size with no block. |

Note*: If you use a value "with block," it gives the order overfill protection. This means that only one order in the group will be routed at a time to prevent an overfill.

#### Adjustable Stops

You can attach one-time adjustments to stop, stop limit, trailing stop and trailing stop limit orders. When you attach an adjusted order, you set a trigger price that triggers a modification of the original (or parent) order, instead of triggering order transmission.

##### Adjusted to Stop

```
# Attached order is a conventional STP order in opposite direction
order = OrderSamples.Stop("SELL" if parent.action == "BUY" else "BUY",
						 parent.totalQuantity, attachedOrderStopPrice)
order.parentId = parent.orderId
#When trigger price is penetrated
order.triggerPrice = triggerPrice
#The parent order will be turned into a STP order
order.adjustedOrderType = "STP"
#With the given STP price
order.adjustedStopPrice = adjustStopPrice
```

##### Adjusted to Stop Limit

```
#Attached order is a conventional STP order
order = OrderSamples.Stop("SELL" if parent.action == "BUY" else "BUY",
													parent.totalQuantity, attachedOrderStopPrice)
order.parentId = parent.orderId
#When trigger price is penetrated
order.triggerPrice = triggerPrice
#The parent order will be turned into a STP LMT order
order.adjustedOrderType = "STP LMT"
#With the given stop price
order.adjustedStopPrice = adjustedStopPrice
#And the given limit price
order.adjustedStopLimitPrice = adjustedStopLimitPrice
```

##### Adjusted to Trail

```
#Attached order is a conventional STP order
order = OrderSamples.Stop("SELL" if parent.action == "BUY" else "BUY",
							parent.totalQuantity, attachedOrderStopPrice)
order.parentId = parent.orderId
#When trigger price is penetrated
order.triggerPrice = triggerPrice
#The parent order will be turned into a TRAIL order
order.adjustedOrderType = "TRAIL"
#With a stop price of...
order.adjustedStopPrice = adjustedStopPrice
#traling by and amount (0) or a percent (100)...
order.adjustableTrailingUnit = trailUnit
#of...
order.adjustedTrailingAmount = adjustedTrailAmount
```

#### Order Conditioning

Conditions allow the activation of orders given a certain criterion...

```
mkt = OrderSamples.MarketOrder("BUY", 100)
# Order will become active if conditioning criteria is met
mkt.conditions.append(
		OrderSamples.PriceCondition(PriceCondition.TriggerMethodEnum.Default,
																208813720, "SMART", 600, False, False))
mkt.conditions.append(OrderSamples.ExecutionCondition("EUR.USD", "CASH", "IDEALPRO", True))
mkt.conditions.append(OrderSamples.MarginCondition(30, True, False))
mkt.conditions.append(OrderSamples.PercentageChangeCondition(15.0, 208813720, "SMART", True, True))
mkt.conditions.append(OrderSamples.TimeCondition("20160118 23:59:59 US/Eastern", True, False))
mkt.conditions.append(OrderSamples.VolumeCondition(208813720, "SMART", False, 100, True))
self.placeOrder(self.nextOrderId(), ContractSamples.EuropeanStock(), mkt)
```

Or cancel them

```
lmt = OrderSamples.LimitOrder("BUY", 100, 20)
# The active order will be cancelled if conditioning criteria is met
lmt.conditionsCancelOrder = True
lmt.conditions.append(
		OrderSamples.PriceCondition(PriceCondition.TriggerMethodEnum.Last,
																208813720, "SMART", 600, False, False))
self.placeOrder(self.nextOrderId(), ContractSamples.EuropeanStock(), lmt)
```

##### Price Conditions 

```
#Conditions have to be created via the OrderCondition.create 
priceCondition = order_condition.Create(OrderCondition.Price)
#When this contract...
priceCondition.conId = conId
#traded on this exchange
priceCondition.exchange = exchange
#has a price above/below
priceCondition.isMore = isMore
priceCondition.triggerMethod = triggerMethod
#this quantity
priceCondition.price = price
#AND | OR next condition (will be ignored if no more conditions are added)
priceCondition.isConjunctionConnection = isConjunction
```

##### Execution Conditions

```
execCondition = order_condition.Create(OrderCondition.Execution)
#When an execution on symbol
execCondition.symbol = symbol
#at exchange
execCondition.exchange = exchange
#for this secType
execCondition.secType = secType
#AND | OR next condition (will be ignored if no more conditions are added)
execCondition.isConjunctionConnection = isConjunction
```

##### Margin Conditions

```
marginCondition = order_condition.Create(OrderCondition.Margin)
#If margin is above/below
marginCondition.isMore = isMore
#given percent
marginCondition.percent = percent
#AND | OR next condition (will be ignored if no more conditions are added)
marginCondition.isConjunctionConnection = isConjunction
```

##### Percentage Conditions

```
pctChangeCondition = order_condition.Create(OrderCondition.PercentChange)
#If there is a price percent change measured against last close price above or below...
pctChangeCondition.isMore = isMore
#this amount...
pctChangeCondition.changePercent = pctChange
#on this contract
pctChangeCondition.conId = conId
#when traded on this exchange...
pctChangeCondition.exchange = exchange
#AND | OR next condition (will be ignored if no more conditions are added)
pctChangeCondition.isConjunctionConnection = isConjunction
```

##### Time Conditions

```
timeCondition = order_condition.Create(OrderCondition.Time)
#Before or after...
timeCondition.isMore = isMore
#this time..
timeCondition.time = time
#AND | OR next condition (will be ignored if no more conditions are added)     
timeCondition.isConjunctionConnection = isConjunction
```

##### Volume Conditions

```
volCond = order_condition.Create(OrderCondition.Volume)
#Whenever contract...
volCond.conId = conId
#When traded at
volCond.exchange = exchange
#reaches a volume higher/lower
volCond.isMore = isMore
#than this...
volCond.volume = volume
#AND | OR next condition (will be ignored if no more conditions are added)
volCond.isConjunctionConnection = isConjunction
```



#### Algorithms

##### IB Algorithms

###### Adaptive Algo

The [Adaptive Algo](https://www.interactivebrokers.com/en/index.php?f=19091) combines IB's Smartrouting capabilities with user-defined priority settings in an effort to achieve further cost efficiency at the point of execution. Using the Adaptive algo leads to better execution prices on average than for regular limit or market orders.

| Parameter        | Description                                                  | Values                    |
| ---------------- | ------------------------------------------------------------ | ------------------------- |
| adaptivePriority | The 'Priority' selector determines the time taken to scan for better execution prices. The 'Urgent' setting scans only briefly, while the 'Patient' scan works more slowly and has a higher chance of achieving a better overall fill for your order. | Urgent > Normal > Patient |

```
baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
AvailableAlgoParams.FillAdaptiveParams(baseOrder, "Normal")
self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillAdaptiveParams(baseOrder: Order, priority: str):
		baseOrder.algoStrategy = "Adaptive"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("adaptivePriority", priority))
```

**Note:** The Time Zone in "startTime" and "endTime" attributes is only applied when the format "hh:mm:ss TMZ" is used

###### ArrivalPrice

The [Arrival Price](https://www.interactivebrokers.com/en/index.php?f=1122) algorithmic order type will attempt to achieve, over the course of the order, the bid/ask midpoint at the time the order is submitted. The Arrival Price algo is designed to keep hidden orders that will impact a high percentage of the average daily volume (ADV). The user-assigned level of risk aversion and the user-defined target percent of average daily volume determine the pace of execution. How quickly the order is submitted during the day is determined by the level of urgency: the higher the urgency, the faster it will be executed, but it will expose it to a greater market impact. Market impact can be lessened by assigning lesser urgency, which is likely to lengthen the duration of the order. The user can set the max percent of ADV from 1 to 50%. The order entry screen allows the user to determine when the order will start and end, regardless of whether or not the full amount of the order has been filled. By checking the box marked Allow trading past end time, the algo will continue to work past the specified end time in an effort to fill the remaining portion of the order.

| Parameter        | Description                              | Values                                 |
| ---------------- | ---------------------------------------- | -------------------------------------- |
| maxPctVol        | Maximum percentage of ADV                | 0.1 (10%) - 0.5 (50%)                  |
| riskAversion     | Urgency/risk aversion                    | Get Done, Aggressive, Neutral, Passive |
| startTime        | Algorithm starting time                  | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ  |
| endTime          | Algorithm ending time                    | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ  |
| allowPastEndTime | Allow trading past end time              | 1 (true) or 0 (false)                  |
| forceCompletion  | Attempt completion by the end of the day | 1 (true) or 0 (false)                  |

```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillArrivalPriceParams(baseOrder, 0.1, "Aggressive", "09:00:00 US/Eastern", "16:00:00 US/Eastern", True, True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillArrivalPriceParams(baseOrder: Order, maxPctVol: float,
             		           riskAversion: str, startTime: str, endTime: str,
              		         forceCompletion: bool, allowPastTime: bool):
		baseOrder.algoStrategy = "ArrivalPx"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
		baseOrder.algoParams.append(TagValue("riskAversion", riskAversion))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("forceCompletion",
   		                                   int(forceCompletion)))
		baseOrder.algoParams.append(TagValue("allowPastEndTime",
                                     int(allowPastTime)))
```

###### Close Price

Investors submitting market or limit orders into the closing auction may adversely affect the closing price, especially when the size of the order is large relative to the average close auction volume. In order to help investors attempting to execute towards the end of the trading session we have developed the [Close Price](https://www.interactivebrokers.com/en/index.php?f=19749) algo Strategy. This algo breaks down large order amounts and determines the timing of order entry so that it will continuously execute in order to minimize slippage. The start and pace of execution are determined by the user who assigns a level of market risk and specifies the target percentage of volume, while the algo considers the prior volatility of the stock.

| Parameter       | Description                              | Values                                 |
| --------------- | ---------------------------------------- | -------------------------------------- |
| maxPctVol       | Maximum percentage of ADV                | 0.1 (10%) - 0.5 (50%)                  |
| riskAversion    | Urgency/risk aversion                    | Get Done, Aggressive, Neutral, Passive |
| startTime       | Algorithm starting time                  | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ  |
| forceCompletion | Attempt completion by the end of the day | 1 (true) or 0 (false)                  |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillClosePriceParams(baseOrder, 0.4, "Neutral", "20180926-06:06:49", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillClosePriceParams(baseOrder: Order, maxPctVol: float, riskAversion: str,
		                     startTime: str, forceCompletion: bool):
		baseOrder.algoStrategy = "ClosePx"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
		baseOrder.algoParams.append(TagValue("riskAversion", riskAversion))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("forceCompletion", int(forceCompletion)))

```

###### Midprice

A [Midprice](https://www.interactivebrokers.com/en/index.php?f=36735) order is designed to split the difference between the bid and ask prices, and fill at the current midpoint of the NBBO or better. Set an optional price cap to define the highest price (for a buy order) or the lowest price (for a sell order) you are willing to accept. Requires TWS 975+. Smart-routing to US stocks only.

- Products: US STK
- Exchanges: Smart-routing only

```
order = Order()
order.action = action
order.orderType = "MIDPRICE"
order.totalQuantity = quantity
order.lmtPrice = priceCap # optional
self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), OrderSamples.Midprice("BUY", 1, 150))
```

###### DarkIce

The [Dark Ice](https://www.interactivebrokers.com/en/index.php?f=3123) order type develops the concept of privacy adopted by orders such as Iceberg or Reserve, using a proprietary algorithm to further hide the volume displayed to the market by the order. Clients can determine the timeframe an order remains live and have the option to allow trading past end time in the event it is unfilled by the stated end time. In order to minimize market impact in the event of large orders, users can specify a display size to be shown to the market different from the actual order size. Additionally, the Dark Ice algo randomizes the display size +/- 50% based upon the probability of the price moving favourably. Further, using calculated probabilities, the algo decides whether to place the order at the limit price or one tick lower than the current offer for buy orders and one tick higher than the current bid for sell orders.

| Parameter        | Description                 | Values                                |
| ---------------- | --------------------------- | ------------------------------------- |
| displaySize      | Order size to be displayed  |                                       |
| startTime        | Algorithm starting time     | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| endTime          | Algorithm ending time       | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| allowPastEndTime | Allow trading past end time | 1 (true) or 0 (false)                 |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillDarkIceParams(baseOrder, 10, "09:00:00 US/Eastern", "16:00:00 US/Eastern", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillDarkIceParams(baseOrder: Order, displaySize: int, startTime: str,
		                  endTime: str, allowPastEndTime: bool):
		baseOrder.algoStrategy = "DarkIce"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("displaySize", displaySize))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("allowPastEndTime",
		                                     int(allowPastEndTime)))
```

###### Accumulate/Distribute

The [Accumulate/Distribute](https://www.interactivebrokers.com/en/index.php?f=1223) algo can help you to achieve the best price for a large volume order without being noticed in the market, and can be set up for high frequency trading. By slicing your order into smaller randomly-sized order increments that are released at random time intervals within a user-defined time period, the algo allows the trading of large blocks of stock and other instruments without being detected in the market. The algo allows limit, market, and relative order types. It is important to keep in mind the API A/D algo will not have all available parameters of the A/D algos that can be created in TWS. Note: The new fields activeTimeStart and activeTimeEnd are used in TWS 971+; startTime and endTime were used previously.

| Parameter         | Description                                                 | Values                     |
| ----------------- | ----------------------------------------------------------- | -------------------------- |
| componentSize     | Quantity of increment                                       | Cannot exceed initial size |
| timeBetweenOrders | Time interval in seconds between each order                 |                            |
| randomizeTime20   | Randomise time period by +/- 20%                            | 1 (true) or 0 (false)      |
| randomizeSize55   | Randomise size by +/- 55%                                   | 1 (true) or 0 (false)      |
| giveUp            | Number associated with the clearing                         |                            |
| catchUp           | Catch up in time                                            | 1 (true) or 0 (false)      |
| waitForFill       | Wait for current order to fill before submitting next order | 1 (true) or 0 (false)      |
| activeTimeStart   | Algorithm starting time                                     | YYYYMMDD-hh:mm:ss TMZ      |
| activeTimeEnd     | Algorithm ending time                                       | YYYYMMDD-hh:mm:ss TMZ      |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		# The Time Zone in "startTime" and "endTime" attributes is ignored and always defaulted to GMT
		AvailableAlgoParams.FillAccumulateDistributeParams(baseOrder, 10, 60, True, True, 1, True, True, "12:00:00", "16:00:00")
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillAccumulateDistributeParams(baseOrder: Order, componentSize: int,
		                               timeBetweenOrders: int, randomizeTime20: bool, randomizeSize55: bool,
		                               giveUp: int, catchUp: bool, waitForFill: bool, startTime: str,
		                               endTime: str):
		baseOrder.algoStrategy = "AD"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("componentSize", componentSize))
		baseOrder.algoParams.append(TagValue("timeBetweenOrders", timeBetweenOrders))
		baseOrder.algoParams.append(TagValue("randomizeTime20",
    		                                 int(randomizeTime20)))
		baseOrder.algoParams.append(TagValue("randomizeSize55",
    		                                 int(randomizeSize55)))
		baseOrder.algoParams.append(TagValue("giveUp", giveUp))
		baseOrder.algoParams.append(TagValue("catchUp", int(catchUp)))
		baseOrder.algoParams.append(TagValue("waitForFill", int(waitForFill)))
		baseOrder.algoParams.append(TagValue("activeTimeStart", startTime))
		baseOrder.algoParams.append(TagValue("activeTimeEnd", endTime))

```

###### Percentage of Volume

The [Percent of Volume ](https://www.interactivebrokers.com/en/index.php?f=1123)algo can limit the contribution of orders to overall average daily volume in order to minimize impact. Clients can set a value between 1-50% to control their participation between stated start and end times. Order quantity and volume distribution over the day is determined using the target percent of volume you entered along with continuously updated volume forecasts calculated from TWS market data. In addition, the algo can be set to avoid taking liquidity, which may help avoid liquidity-taker fees and could result in liquidity-adding rebates. By checking the Attempt to never take liquidity box, the algo is discouraged from hitting the bid or lifting the offer if possible. However, this may also result in greater deviations from the benchmark, and in partial fills, since the posted bid/offer may not always get hit as the price moves up/down. IB will use best efforts not to take liquidity when this box is checked, however, there will be times that it cannot be avoided.

| Parameter | Description                     | Values                                |
| --------- | ------------------------------- | ------------------------------------- |
| pctVol    | Target Percentage               | 0.1 (10%) - 0.5 (50%)                 |
| startTime | Algorithm starting time         | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| endTime   | Algorithm ending time           | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| noTakeLiq | Attempt to never take liquidity | 1 (true) or 0 (false)                 |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillPctVolParams(baseOrder, 0.5, "12:00:00 US/Eastern", "14:00:00 US/Eastern", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillPctVolParams(baseOrder: Order, pctVol: float, startTime: str,
    		             endTime: str, noTakeLiq: bool):
		baseOrder.algoStrategy = "PctVol"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("pctVol", pctVol))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))
```

###### TWAP

The [TWAP](https://www.interactivebrokers.com/en/index.php?f=1125) algo aims to achieve the time-weighted average price calculated from the time you submit the order to the time it completes. Incomplete orders at the end of the stated completion time will continue to fill if the box 'allow trading past end time' is checked. Users can set the order to trade only when specified conditions are met. Those user-defined inputs include when the order is marketable, when the midpoint matches the required price, when the same side (buy or sell) matches to make the order marketable or when the last traded price would make the order marketable. For the TWAP algo, the average price calculation is calculated from the order entry time through the close of the market and will only attempt to execute when the criterion is met. The order may not fill throughout its stated duration and so the order is not guaranteed. TWAP is available for all US equities.

| Parameter        | Description                 | Values                                                       |
| ---------------- | --------------------------- | ------------------------------------------------------------ |
| strategyType     | Trade strategy              | Marketable, Matching, Midpoint, Matching Same Side, Matching Last |
| startTime        | Algorithm starting time     | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ                        |
| endTime          | Algorithm ending time       | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ                        |
| allowPastEndTime | Allow trading past end time | 1 (true) or 0 (false)                                        |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillTwapParams(baseOrder, "Marketable", "09:00:00 US/Eastern", "16:00:00 US/Eastern", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillTwapParams(baseOrder: Order, strategyType: str, startTime: str,
         		       endTime: str, allowPastEndTime: bool):
		baseOrder.algoStrategy = "Twap"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("strategyType", strategyType))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("allowPastEndTime",
                                     int(allowPastEndTime)))
```

###### Price Variant Percentage of Volume Strategy

[Price Variant Percentage of Volume Strategy](https://www.interactivebrokers.com/en/index.php?f=14369#collapse03) - This algo allows you to participate in volume at a user-defined rate that varies over time depending on the market price of the security. This algo allows you to buy more aggressively when the price is low and be more passive as the price increases, and just the opposite for sell orders. The order quantity and volume distribution over the time during which the order is active is determined using the target percent of volume you entered along with continuously updated volume forecasts calculated from TWS market data.

| Parameter    | Description                     | Values                                |
| ------------ | ------------------------------- | ------------------------------------- |
| pctVol       | Target Percentage               | 0.1 (10%) - 0.5 (50%)                 |
| deltaPctVol  | Target Percentage Change Rate   | 0.1 (10%) - 0.5 (50%)                 |
| minPctVol4Px | Minimum Target Percentage       | 0.1 (10%) - 0.5 (50%)                 |
| maxPctVol4Px | Maximum Target Percentage       | 0.1 (10%) - 0.5 (50%)                 |
| startTime    | Algorithm starting time         | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| endTime      | Algorithm ending time           | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| noTakeLiq    | Attempt to never take liquidity | 1 (true) or 0 (false)                 |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillPriceVariantPctVolParams(baseOrder, 0.1, 0.05, 0.01, 0.2, "12:00:00 US/Eastern", "14:00:00 US/Eastern", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillPriceVariantPctVolParams(baseOrder: Order, pctVol: float,
              		               deltaPctVol: float, minPctVol4Px: float,
               		               maxPctVol4Px: float, startTime: str,
                  		           endTime: str, noTakeLiq: bool):
		baseOrder.algoStrategy = "PctVolPx"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("pctVol", pctVol))
		baseOrder.algoParams.append(TagValue("deltaPctVol", deltaPctVol))
		baseOrder.algoParams.append(TagValue("minPctVol4Px", minPctVol4Px))
		baseOrder.algoParams.append(TagValue("maxPctVol4Px", maxPctVol4Px))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))

```

###### Size Variant Percentage of Volume Strategy

[Size Variant Percentage of Volume Strategy](https://www.interactivebrokers.com/en/index.php?f=14369#collapse02) - This algo allows you to participate in volume at a user-defined rate that varies over time depending on the remaining size of the order. Define the target percent rate at the start time (Initial Participation Rate) and at the end time (Terminal Participation Rate), and the algo calculates the participation rate over time between the two based on the remaining order size. This allows the order to be more aggressive initially and less aggressive toward the end, or vice versa.

| Parameter   | Description                     | Values                                |
| ----------- | ------------------------------- | ------------------------------------- |
| startPctVol | Initial Target Percentage       | 0.1 (10%) - 0.5 (50%)                 |
| endPctVol   | Terminal Target Percentage      | 0.1 (10%) - 0.5 (50%)                 |
| startTime   | Algorithm starting time         | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| endTime     | Algorithm ending time           | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| noTakeLiq   | Attempt to never take liquidity | 1 (true) or 0 (false)                 |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillSizeVariantPctVolParams(baseOrder, 0.2, 0.4, "12:00:00 US/Eastern", "14:00:00 US/Eastern", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillSizeVariantPctVolParams(baseOrder: Order, startPctVol: float,
         	                      endPctVol: float, startTime: str,
                                endTime: str, noTakeLiq: bool):
		baseOrder.algoStrategy = "PctVolSz"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("startPctVol", startPctVol))
		baseOrder.algoParams.append(TagValue("endPctVol", endPctVol))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))
```

###### Time Variant Percentage of Volume Strategy

[Time Variant Percentage of Volume Strategy](https://www.interactivebrokers.com/en/index.php?f=14369#collapse01) - This algo allows you to participate in volume at a user-defined rate that varies with time. Define the target percent rate at the start time and at the end time, and the algo calculates the participation rate over time between the two. This allows the order to be more aggressive initially and less aggressive toward the end, or vice versa.

| Parameter   | Description                     | Values                                |
| ----------- | ------------------------------- | ------------------------------------- |
| startPctVol | Initial Target Percentage       | 0.1 (10%) - 0.5 (50%)                 |
| endPctVol   | Terminal Target Percentage      | 0.1 (10%) - 0.5 (50%)                 |
| startTime   | Algorithm starting time         | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| endTime     | Algorithm ending time           | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| noTakeLiq   | Attempt to never take liquidity | 1 (true) or 0 (false)                 |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillTimeVariantPctVolParams(baseOrder, 0.2, 0.4, "12:00:00 US/Eastern", "14:00:00 US/Eastern", True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillTimeVariantPctVolParams(baseOrder: Order, startPctVol: float,
                            endPctVol: float, startTime: str,
                            endTime: str, noTakeLiq: bool):
		baseOrder.algoStrategy = "PctVolTm"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("startPctVol", startPctVol))
		baseOrder.algoParams.append(TagValue("endPctVol", endPctVol))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))
```

###### VWAP

IB's best-efforts [VWAP](https://www.interactivebrokers.com/en/index.php?f=1124) algo seeks to achieve the Volume-Weighted Average price (VWAP), calculated from the time you submit the order to the close of the market.

Best-efforts VWAP algo is a lower-cost alternative to the Guaranteed VWAP (no longer supported) that enables the user to attempt never to take liquidity while also trading past the end time. Because the order may not be filled on the bid or at the ask prices, there is a trade-off with this algo. The order may not fully fill if the user is attempting to avoid liquidity-taking fees and/or maximize liquidity-adding rebates, and may miss the benchmark by asking to stay on the bid or ask. The user can determine the maximum percentage of average daily volume (up to 50%) his order will comprise. The system will generate the VWAP from the time the order is entered through the close of trading, and the order can be limited to trading over a pre-determined period. The user can request the order to continue beyond its stated end time if unfilled at the end of the stated period. The best-efforts VWAP algo is available for all US equities.

| Parameter        | Description                                                  | Values                                |
| ---------------- | ------------------------------------------------------------ | ------------------------------------- |
| maxPctVol        | Maximum percentage of average daily volume                   | 0.1 (10%) - 0.5 (50%)                 |
| startTime        | Algorithm starting time                                      | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| endTime          | Algorithm ending time                                        | hh:mm:ss TMZ or YYYYMMDD-hh:mm:ss TMZ |
| allowPastEndTime | Allow trading past end time                                  | 1 (true) or 0 (false)                 |
| noTakeLiq        | Attempt to never take liquidity                              | 1 (true) or 0 (false)                 |
| speedUp          | Compensate for the decreased fill rate due to presence of limit price | 1 (true) or 0 (false)                 |



```
		baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
		AvailableAlgoParams.FillVwapParams(baseOrder, 0.2, "09:00:00 US/Eastern", "16:00:00 US/Eastern", True, True)
		self.placeOrder(self.nextOrderId(), ContractSamples.USStockAtSmart(), baseOrder)
...
@staticmethod
def FillVwapParams(baseOrder: Order, maxPctVol: float, startTime: str,
               endTime: str, allowPastEndTime: bool, noTakeLiq: bool):
		baseOrder.algoStrategy = "Vwap"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
		baseOrder.algoParams.append(TagValue("startTime", startTime))
		baseOrder.algoParams.append(TagValue("endTime", endTime))
		baseOrder.algoParams.append(TagValue("allowPastEndTime",
    		                                 int(allowPastEndTime)))
		baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))
```

###### Balance Impact Risk

The [Balance Impact Risk](https://www.interactivebrokers.com/en/index.php?f=1120) balances the market impact of trading the option with the risk of price change over the time horizon of the order. This strategy considers the user-assigned level of risk aversion to define the pace of the execution, along with the user-defined target percent of volume.

| Parameter       | Description                                | Values                                 |
| --------------- | ------------------------------------------ | -------------------------------------- |
| maxPctVol       | Maximum percentage of average daily volume | 0.1 (10%) - 0.5 (50%)                  |
| riskAversion    | Urgency/risk aversion                      | Get Done, Aggressive, Neutral, Passive |
| forceCompletion | Attempt completion by the end of the day   | 1 (true) or 0 (false)                  |



```
	baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
	AvailableAlgoParams.FillBalanceImpactRiskParams(baseOrder, 0.1, "Aggressive", True) 				self.placeOrder(self.nextOrderId(), ContractSamples.USOptionContract(), baseOrder)
...
@staticmethod
def FillBalanceImpactRiskParams(baseOrder: Order, maxPctVol: float,
																riskAversion: str, forceCompletion: bool):
		baseOrder.algoStrategy = "BalanceImpactRisk"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
		baseOrder.algoParams.append(TagValue("riskAversion", riskAversion))
		baseOrder.algoParams.append(TagValue("forceCompletion",
																					int(forceCompletion)))
```

###### Minimise Impact

The [Minimise Impact](https://www.interactivebrokers.com/en/index.php?f=1121) algo minimises market impact by slicing the order over time to achieve a market average without going over the given maximum percentage value.

| Parameter | Description                                | Values                |
| --------- | ------------------------------------------ | --------------------- |
| maxPctVol | Maximum percentage of average daily volume | 0.1 (10%) - 0.5 (50%) |



```
baseOrder = OrderSamples.LimitOrder("BUY", 1000, 1)
...
AvailableAlgoParams.FillMinImpactParams(baseOrder, 0.3)
self.placeOrder(self.nextOrderId(), ContractSamples.USOptionContract(), baseOrder)
...
@staticmethod
def FillMinImpactParams(baseOrder: Order, maxPctVol: float):
		baseOrder.algoStrategy = "MinImpact"
		baseOrder.algoParams = []
		baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
```



## Order Management

### Placing Orders

#### The Next Valid Identifier

Perhaps the most important event received after successfully connecting to the TWS is the [IBApi.EWrapper.nextValidId](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a09c07727efd297e438690ab42838d332), which is also triggered after invoking the [IBApi.EClient.reqIds](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aecea365530f40e7b56529238c9dd2f4a) method. As its name indicates, the nextValidId event provides the next valid identifier needed to place an order. This identifier is nothing more than the next number in the sequence. This means that if there is a single client application submitting orders to an account, it does not have to obtain a new valid identifier every time it needs to submit a new order. It is enough to increase the last value received from the nextValidId method by one. For example, if the valid identifier for your first API order is 1, the next valid identifier would be 2 and so on.

However, if there are multiple client applications connected to one account, it is necessary to use an order ID with new orders that is greater than all previous order IDs returned to the client application in openOrder or orderStatus callbacks. For instance, if the client is set as the Master client, it will automatically receive order status and trade callbacks from orders placed by other clients. In such a case, any orderID used in placeOrder must be greater than the orderIDs returned in these status callbacks. Alternatively, if the function reqAllOpenOrders is used by a client, subsequent orders placed by that client must have order IDs greater than the order IDs of all orders returned because of that function call. You can always use the [IBApi.EClient.reqIds](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aecea365530f40e7b56529238c9dd2f4a) method in the event that your client application loses track of the sequence.

```
# The parameter is always ignored.
self.reqIds(-1)
```

The above will result in [IBApi.EWrapper.nextValidId](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a09c07727efd297e438690ab42838d332) callback being invoked:

- ```
  class TestWrapper(wrapper.EWrapper):
  ...
  def nextValidId(self, orderId: int):
  super().nextValidId(orderId)
   
  logging.debug("setting nextValidOrderId: %d", orderId)
  self.nextValidOrderId = orderId
  print("NextValidId:", orderId)
  ```

#### **The next valid identifier is persistent between TWS sessions.**

If necessary, you can reset the order ID sequence within the API Settings dialogue. Note however that the order sequence Id can only be reset if there are no active API orders.

#### Placing Orders

Orders are submitted via the [IBApi.EClient.placeOrder](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa6ff6f6455c551bef9d66c34d1c8586c) method. From the snippet below, note how a variable holding the nextValidId is incremented automatically:

```
self.simplePlaceOid = self.nextOrderId()
self.placeOrder(self.simplePlaceOid, ContractSamples.USStock(),
OrderSamples.LimitOrder("SELL", 1, 50))
```

Immediately after the order was submitted correctly, the TWS will start sending events concerning the order's activity via [IBApi.EWrapper.openOrder](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#aa05258f1d005accd3efc0d60bc151407) and [IBApi.EWrapper.orderStatus](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a27ec36f07dff982f50968c8a8887d676)

- Advisors executing allocation orders will receive execution details and commissions for the allocation order itself. To receive allocation details and commissions for a specific subaccount [IBApi.EClient.reqExecutions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ae17dbb7d7c37591de983b2b74af2d9b5) can be used.
- An order can be sent to TWS but not transmitted to the IB server by setting the [IBApi.Order.Transmit](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#aaa1c4f31b9580ee0715edcd78a51cbec) flag in the order class to **False**. Untransmitted orders will only be available within that TWS session (not for other usernames) and will be cleared on restart. Also, they can be cancelled or transmitted from the API but not viewed while they remain in the "untransmitted" state.

#### Advanced order rejection

With **TWS 10.14** and **TWS API 10.14** there have been added [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) ***advancedErrorOverride\*** and [IBApi.EWrapper.error](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a7dfc221702ca65195609213c984729b8) ***advancedOrderRejectJson\*** properties. They have been introduced to extend [Cryptocurrency](https://interactivebrokers.github.io/tws-api/cryptocurrency.html) offerings across other regions.

- ***advancedErrorOverride\*** accepts a comma-separated list with parameters containing error tags. This list will override the mentioned errors and proceed with the order placement.
- ***advancedOrderRejectJson\*** returns order reject description in JSON format. The JSON responses can be used to add fields to ***advancedErrorOverride\***.
  The response your API client will receive from our back end via reject message will contain a FIX Tag **8230**. This FIX Tag is for order rejection message.
  For example, the response will contain code **8229=IBDBUYTX** which can be passed in **8229** (advancedErrorOverride) field in [IBApi::EClient::placeOrder](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa6ff6f6455c551bef9d66c34d1c8586c) method.
  The tag values of **8229** can contain a comma-separated list of advanced error override codes. For example, **8229=IBDBUYTX,MOBILEPH**, will override both of the rejections.
- Additionally, a new API Setting has been introduced: **Show advanced order reject in UI always**
  If *checked*, the order reject will not be sent to API.

#### The openOrder callback

The [IBApi.EWrapper.openOrder](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#aa05258f1d005accd3efc0d60bc151407) method delivers an [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) object representing an open order within the system. In addition, [IBApi.EWrapper.openOrder](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#aa05258f1d005accd3efc0d60bc151407) returns an an [IBApi.OrderState](https://interactivebrokers.github.io/tws-api/classIBApi_1_1OrderState.html) object that is used to return estimated pre-trade margin and commission information in response to invoking [IBApi.EClient.placeOrder](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa6ff6f6455c551bef9d66c34d1c8586c) with a [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) object that has the flag [IBApi.Order.WhatIf](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a657f43100729808a833b9c01adeeb165) flag set to True. See also: [Checking Margin Changes](http://interactivebrokers.github.io/tws-api/margin.html).

```
class TestWrapper(wrapper.EWrapper):
...
		def openOrder(self, orderId: OrderId, contract: Contract, order: Order,
orderState: OrderState):
				super().openOrder(orderId, contract, order, orderState)
				print("OpenOrder. PermId:", intMaxString(order.permId), "ClientId:", intMaxString(order.clientId), " OrderId:", intMaxString(orderId), 
							"Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
							"Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
							"TotalQty:", decimalMaxString(order.totalQuantity), "CashQty:", floatMaxString(order.cashQty), 
							"LmtPrice:", floatMaxString(order.lmtPrice), "AuxPrice:", floatMaxString(order.auxPrice), "Status:", orderState.status,
							"MinTradeQty:", intMaxString(order.minTradeQty), "MinCompeteSize:", intMaxString(order.minCompeteSize),
							"competeAgainstBestOffset:", "UpToMid" if order.competeAgainstBestOffset == COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID else floatMaxString(order.competeAgainstBestOffset),
							"MidOffsetAtWhole:", floatMaxString(order.midOffsetAtWhole),"MidOffsetAtHalf:" ,floatMaxString(order.midOffsetAtHalf),
							"FAGroup:", order.faGroup, "FAMethod:", order.faMethod)
 
				order.contract = contract
				self.permId2ord[order.permId] = order
```

#### The orderStatus callback

The [IBApi.EWrapper.orderStatus](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a27ec36f07dff982f50968c8a8887d676) method contains all relevant information on the current status of the order execution-wise (i.e. amount filled and pending, filling price, etc.).

```
class TestWrapper(wrapper.EWrapper):
...
def orderStatus(self, orderId: OrderId, status: str, filled: Decimal,
								remaining: Decimal, avgFillPrice: float, permId: int,
								parentId: int, lastFillPrice: float, clientId: int,
								whyHeld: str, mktCapPrice: float):
		super().orderStatus(orderId, status, filled, remaining,
												avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
		print("OrderStatus. Id:", orderId, "Status:", status, "Filled:", decimalMaxString(filled),
					"Remaining:", decimalMaxString(remaining), "AvgFillPrice:", floatMaxString(avgFillPrice),
					"PermId:", intMaxString(permId), "ParentId:", intMaxString(parentId), "LastFillPrice:",
					floatMaxString(lastFillPrice), "ClientId:", intMaxString(clientId), "WhyHeld:",
					whyHeld, "MktCapPrice:", floatMaxString(mktCapPrice))
```

**Automatic Order Status Messages** (without invoking reqOpenOrders or reqAllOpenOrders)

- Clients with the ID of the client submitting the order will receive order status messages indicating changes in the order status.
- The client with [Master Client ID](https://interactivebrokers.github.io/tws-api/initial_setup.html#master_client) (set in TWS/IBG) will receive order status messages for all clients.
- Client ID 0 will receive order status messages for its own (client ID 0) orders and also for orders submitted manually from TWS.

**Possible Order States**

- **ApiPending** - indicates order has not yet been sent to IB server, for instance if there is a delay in receiving the security definition. Uncommonly received.
- **PendingSubmit** - indicates the order was sent from TWS, but confirmation has not been received that it has been received by the destination. Most commonly because exchange is closed.
- **PendingCancel** - indicates that a request has been sent to cancel an order but confirmation has not been received of its cancellation.
- **PreSubmitted** - indicates that a simulated order type has been accepted by the IB system and that this order has yet to be elected. The order is held in the IB system until the election criteria are met. At that time the order is transmitted to the order destination as specified.
- **Submitted** - indicates that your order has been accepted at the order destination and is working.
- **ApiCancelled** - after an order has been submitted and before it has been acknowledged, an API client can request its cancellation, producing this state.
- **Cancelled** - indicates that the balance of your order has been confirmed cancelled by the IB system. This could occur unexpectedly when IB or the destination has rejected your order. For example, if your order is subject to price checks, it could be cancelled, as explained in [Order Placement Considerations](https://interactivebrokers.github.io/tws-api/automated_considerations.html#order_placement)
- **Filled** - indicates that the order has been completely filled.
- Inactive - indicates an order is not working, possible reasons include:
  - it is invalid or triggered an error. A corresponding error code is expected to the error() function.
    - This error may be a reject, for example a regulatory size reject. See [Order Placement Considerations](https://interactivebrokers.github.io/tws-api/automated_considerations.html#order_placement)
  - the order is to short shares but the order is being held while shares are being located.
  - an order is placed manually in TWS while the exchange is closed.
  - an order is blocked by TWS due to a precautionary setting and appears there in an untransmitted state

**Important notes** concerning [IBApi.EWrapper.orderStatus](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a27ec36f07dff982f50968c8a8887d676) :

- Typically there are duplicate orderStatus messages with the same information that will be received by a client. This corresponds to messages sent back from TWS, the IB server, or the exchange.
- There are not guaranteed to be orderStatus callbacks for every change in order status. For example with market orders when the order is accepted and executes immediately, there commonly will not be any corresponding orderStatus callbacks. For that reason it is recommended to monitor the [IBApi.EWrapper.execDetails](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a09f82de3d0666d13b00b5168e8b9313d) function in addition to [IBApi.EWrapper.orderStatus](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a27ec36f07dff982f50968c8a8887d676).
- Beginning in API v973.04, a parameter mktCapPrice is included in the orderStatus callback. If an order has been [price-capped](https://www.interactivebrokers.com/en/index.php?f=14186), mktCapPrice will indicate the price at which it has been capped.

#### Attaching Orders

Advanced orders such as [Bracket Orders](https://interactivebrokers.github.io/tws-api/bracket_order.html) or [Hedging](https://interactivebrokers.github.io/tws-api/hedging.html) involve attaching child orders to a parent. This can be easily done via the [IBApi.Order.ParentId](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a37c8c925a01a8a35a1dfb942afeba264) attribute by assigning a child order's [IBApi.Order.ParentId](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a37c8c925a01a8a35a1dfb942afeba264) to an existing order's [IBApi.Order.OrderId](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#ac2926db25ae147030a1cf519c07d86a6). When an order is attached to another, the system will keep the child order 'on hold' until its parent fills. Once the parent order is completely filled, its children will automatically become active.

### Modifying Orders

Modification of an API order can be done if the API client is connected to a session of TWS with the same username of TWS and using the same API client ID. The function [IBApi.EClient.placeOrder](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa6ff6f6455c551bef9d66c34d1c8586c) can then be called with the same fields as the open order, except for the parameter to modify. This includes the [IBApi.Order.OrderId](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#ac2926db25ae147030a1cf519c07d86a6), which must match the [IBApi.Order.OrderId](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#ac2926db25ae147030a1cf519c07d86a6) of the **open** order. It is not generally recommended to try to change order fields aside from order price, size, and tif (for DAY -> IOC modifications). To change other parameters, it might be preferable to instead cancel the open order, and create a new one.

### Cancelling Orders

An order can be cancelled from the API with the functions [IBApi::EClient::cancelOrder](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ab9d166f15b98d2ba2ea707c281e698a1) and [IBApi::EClient::reqGlobalCancel](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a66ad7a4820c5be21ebde521d59a50053). cancelOrder can only be used to cancel an order that was placed originally by a client with the same client ID (or from TWS for client ID 0). It takes one argument, which is the original order ID.

```
self.cancelOrder(self.simplePlaceOid, "")
```

[IBApi::EClient::reqGlobalCancel](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a66ad7a4820c5be21ebde521d59a50053) will cancel all open orders, regardless of how they were originally placed.

```
self.reqGlobalCancel()
```



#### Retrieving currently active orders

As long as an order is active, it is possible to retrieve it using the TWS API. Orders submitted via the TWS API will always be bound to the client application (i.e. client Id) they were submitted from meaning only the submitting client will be able to modify the placed order. Three different methods are provided to allow for maximum flexibility. Active orders will be returned via the [IBApi.EWrapper.openOrder](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#aa05258f1d005accd3efc0d60bc151407) and [IBApi.EWrapper.orderStatus](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a27ec36f07dff982f50968c8a8887d676) methods as already described in [The openOrder callback](https://interactivebrokers.github.io/tws-api/order_submission.html#open_order) and [The orderStatus callback](https://interactivebrokers.github.io/tws-api/order_submission.html#order_status) sections

**Note:** it is not possible to obtain cancelled or fully filled orders.

##### API client's orders

The [IBApi.EClient.reqOpenOrders](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a11e9c0dce640a3b8ed7a221792412c05) method allows to obtain all active orders submitted by the client application connected with the exact same client Id with which the order was sent to the TWS. If client 0 invokes reqOpenOrders, it will cause **currently** open orders placed from TWS manually to be 'bound', i.e. assigned an order ID so that they can be modified or cancelled by the API client 0. In API versions after 973.07 there will be [IBApi.EWrapper.orderBound](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a1f900736d47e30361e0c32e11eff1056) callback in response to newly bound orders that indicates the mapping between the permID (unique account-wide) and API Order ID (specific to an API client). In the API settings in Global Configuration, is a setting checked by default "Use negative numbers to bind automatic orders" which will specify how manual TWS orders are assigned an API order ID.

```
self.reqOpenOrders()
```

##### All submitted orders

To obtain those orders created via the TWS API regardless of the submitting client application, make use of the [IBApi.EClient.reqAllOpenOrders](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa2c9a012884dd53311a7a7c6a326306c) function.

```
self.reqAllOpenOrders()
```

##### Manually submitted orders

Finally, [IBApi.EClient.reqAutoOpenOrders](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#abe8e30367fff33b9a1171a4580029016) can only be invoked by client with ID 0. It will cause future orders placed from TWS to be 'bound', i.e. assigned an order ID such that they can be accessed by the cancelOrder or placeOrder (for modification) functions by client ID 0.

```
self.reqAutoOpenOrders(True)
```

**Important:** only those applications connecting with client Id 0 will be able to take over manually submitted orders

##### Receiving Order Information

Active orders will be delivered via [The openOrder callback](https://interactivebrokers.github.io/tws-api/order_submission.html#open_order) and [The orderStatus callback](https://interactivebrokers.github.io/tws-api/order_submission.html#order_status) callbacks. When all orders have been sent to the client application you will receive a [IBApi.EWrapper.openOrderEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ab86caf7ed61e14d9b5609e8dd60b93e1) event:

```
class TestWrapper(wrapper.EWrapper):
...

		def openOrder(self, orderId: OrderId, contract: Contract, order: Order,
									orderState: OrderState):

				super().openOrder(orderId, contract, order, orderState)
				print("OpenOrder. PermId:", intMaxString(order.permId), "ClientId:", intMaxString(order.clientId), " OrderId:", intMaxString(orderId), 
							"Account:", order.account, "Symbol:", contract.symbol, "SecType:", contract.secType,
							"Exchange:", contract.exchange, "Action:", order.action, "OrderType:", order.orderType,
							"TotalQty:", decimalMaxString(order.totalQuantity), "CashQty:", floatMaxString(order.cashQty), 
							"LmtPrice:", floatMaxString(order.lmtPrice), "AuxPrice:", floatMaxString(order.auxPrice), "Status:", orderState.status,
							"MinTradeQty:", intMaxString(order.minTradeQty), "MinCompeteSize:", intMaxString(order.minCompeteSize),
							"competeAgainstBestOffset:", "UpToMid" if order.competeAgainstBestOffset == COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID else floatMaxString(order.competeAgainstBestOffset),
							"MidOffsetAtWhole:", floatMaxString(order.midOffsetAtWhole),"MidOffsetAtHalf:" ,floatMaxString(order.midOffsetAtHalf),
							"FAGroup:", order.faGroup, "FAMethod:", order.faMethod)

				order.contract = contract
				self.permId2ord[order.permId] = order
...
def orderStatus(self, orderId: OrderId, status: str, filled: Decimal,
								remaining: Decimal, avgFillPrice: float, permId: int,
								parentId: int, lastFillPrice: float, clientId: int,
								whyHeld: str, mktCapPrice: float):
				super().orderStatus(orderId, status, filled, remaining,
														avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
				print("OrderStatus. Id:", orderId, "Status:", status, "Filled:", decimalMaxString(filled),
							"Remaining:", decimalMaxString(remaining), "AvgFillPrice:", floatMaxString(avgFillPrice),
							"PermId:", intMaxString(permId), "ParentId:", intMaxString(parentId), "LastFillPrice:",
							floatMaxString(lastFillPrice), "ClientId:", intMaxString(clientId), "WhyHeld:",
							whyHeld, "MktCapPrice:", floatMaxString(mktCapPrice))
...
		def openOrderEnd(self):
				super().openOrderEnd()
				print("OpenOrderEnd") 

				logging.debug("Received %d openOrders", len(self.permId2ord))
```

##### Order Binding Notification

When an order is bound by API client 0 there will be callback to [IBApi::EWrapper::orderBound](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a1f900736d47e30361e0c32e11eff1056).
This indicates the mapping between API order ID and permID.
**not yet implemented

```
def orderBound(self, orderId: int, apiClientId: int, apiOrderId: int):
		super().orderBound(orderId, apiClientId, apiOrderId)
		print("OrderBound.", "OrderId:", intMaxString(orderId), "ApiClientId:", intMaxString(apiClientId), "ApiOrderId:", intMaxString(apiOrderId))
```

#### Executions and Commissions

When an order is filled either fully or partially, the [IBApi.EWrapper.execDetails](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a09f82de3d0666d13b00b5168e8b9313d) and [IBApi.EWrapper.commissionReport](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a7ebfc18d5d03189ab5bf895db4a1a204) events will deliver [IBApi.Execution](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Execution.html) and [IBApi.CommissionReport](https://interactivebrokers.github.io/tws-api/classIBApi_1_1CommissionReport.html) objects. This allows to obtain the full picture of the order's execution and the resulting commissions.

- Advisors executing allocation orders will receive execution details and commissions for the allocation order itself. To receive allocation details and commissions for a specific subaccount [IBApi.EClient.reqExecutions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ae17dbb7d7c37591de983b2b74af2d9b5) can be used.

**Important:** To receive commissions reports for all clients it is necessary to connect as the [Master Client ID](https://interactivebrokers.github.io/tws-api/initial_setup.html#master_client).

```
class TestWrapper(wrapper.EWrapper):
...
		def execDetails(self, reqId: int, contract: Contract, execution: Execution):
				super().execDetails(reqId, contract, execution)
				print("ExecDetails. ReqId:", reqId, "Symbol:", contract.symbol, "SecType:", contract.secType, "Currency:", contract.currency, execution)
...
		def commissionReport(self, commissionReport: CommissionReport):
				super().commissionReport(commissionReport)
				print("CommissionReport.", commissionReport)
```



- Note if a correction to an execution is published it will be received as an additional [IBApi.EWrapper.execDetails](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a09f82de3d0666d13b00b5168e8b9313d) callback with all parameters identical except for the execID in the Execution object. The execID will differ only in the digits after the final period.

##### Requesting Executions

[IBApi.Execution](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Execution.html) and [IBApi.CommissionReport](https://interactivebrokers.github.io/tws-api/classIBApi_1_1CommissionReport.html) can be requested on demand via the [IBApi.EClient.reqExecutions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ae17dbb7d7c37591de983b2b74af2d9b5) method which receives a [IBApi.ExecutionFilter](https://interactivebrokers.github.io/tws-api/classIBApi_1_1ExecutionFilter.html) object as parameter to obtain only those executions matching the given criteria. An empty [IBApi.ExecutionFilter](https://interactivebrokers.github.io/tws-api/classIBApi_1_1ExecutionFilter.html) object can be passed to obtain all previous executions.

```
self.reqExecutions(10001, ExecutionFilter())
```

Once all matching executions have been delivered, an [IBApi.EWrapper.execDetailsEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ac9b605c48d60da99ef595d2bc7ca39e2) event will be triggered.

```
class TestWrapper(wrapper.EWrapper):
...
		def execDetailsEnd(self, reqId: int):
				super().execDetailsEnd(reqId)
				print("ExecDetailsEnd. ReqId:", reqId)
```

**Important:** By default, only those executions occurring since midnight for that particular account will be delivered. If you want to request executions up to last 7 days, TWS's Trade Log setting *"Show trades for ..."* must be adjusted to your requirement. Please note, that *IB Gateway* would be unable to change the Trade Log's settings, thus limited to only executions since midnight to be delivered.

## Checking Margin Changes

From the API it is possible to check how a specified trade execution is expected to change the account margin requirements for an account in real time. This is done by creating an Order object which has the [IBApi.Order.WhatIf](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a657f43100729808a833b9c01adeeb165) flag set to true. By default the whatif boolean in Order has a false value, but if set to True in an Order object with is passed to [IBApi.EClient.placeOrder](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa6ff6f6455c551bef9d66c34d1c8586c), instead of sending the order to a destination the IB server it will undergo a credit check for the expected post-trade margin requirement. The estimated post-trade margin requirement is returned to the [IBApi.OrderState](https://interactivebrokers.github.io/tws-api/classIBApi_1_1OrderState.html) object.

## Trigger Methods

The Trigger Method defined in the [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) class specifies how simulated stop, stop-limit, and trailling stops, and conditional orders are triggered. Valid values are:

- 0 - The default method for instrument
- 1 - "Double bid/ask" function, where stop orders are triggered based on two consecutive bid or ask prices.
- 2 - "Last" function, where stop orders are triggered based on the last price
- 3 - "Double last" function
- 4 - Bid/ask function
- 7 - Last or bid/ask function
- 8 - Mid-point function

Below is a table which indicates whether a given secType is compatible with bid/ask-driven or last-driven trigger methods (method 7 only used in iBot alerts)

| secType     | Bid/Ask-driven (1, 4, 8) | Last-driven (2, 3) | Default behavior                    | Notes                                     |
| ----------- | ------------------------ | ------------------ | ----------------------------------- | ----------------------------------------- |
| STK         | yes                      | yes                | Last                                | The double bid/ask is used for OTC stocks |
| CFD         | yes                      | yes                | Last                                |                                           |
| CFD - Index | yes                      | n/a                | n/a                                 | Ex IBUS500                                |
| OPT         | yes                      | yes                | US OPT: Double bid/ask, Other: Last |                                           |
| FOP         | yes                      | yes                | Last                                |                                           |
| WAR         | yes                      | yes                | Last                                |                                           |
| IOPT        | yes                      | yes                | Last                                |                                           |
| FUT         | yes                      | yes                | Last                                |                                           |
| COMBO       | yes                      | yes                | Last                                |                                           |
| CASH        | yes                      | n/a                | Bid/ask                             |                                           |
| CMDTY       | yes                      | n/a                | Last                                |                                           |
| IND         | n/a                      | yes                | n/a                                 | For conditions only                       |

**Important notes** :

- If an incompatible triggerMethod and secType are used in your API order, the order may never trigger.
- These trigger methods only apply to stop orders simulated by IB. If a stop-variant is handled natively, the trigger method specified is ignored. See our [Stop Orders](https://www.interactivebrokers.com/en/index.php?f=609) page for more information.
