# Error Handling

When a client application sends a message to TWS that requires a has an expected response (i.e., placing an order, requesting market data, subscribing to account updates, etc.), response, which either always **1)** respond with the relevant data or **2)** send an error message to [IBApi::EWrapper::error](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a7dfc221702ca65195609213c984729b8).

- **Exceptions when no response can occur**: If a market data request is made during a competing session (when a paper user is logged into simultaneously on a different computer from the associated live user) before TWS v972, there will not be a message returned from TWS. Also, if a request is made prior to full establishment of connection (denoted by a returned 2104 or 2106 error code *"Data Server is Ok"*), there may not be a response to the request.

Error messages sent by the TWS are handled by the [IBApi.EWrapper.error](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a7dfc221702ca65195609213c984729b8) method. The [IBApi.EWrapper.error](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a7dfc221702ca65195609213c984729b8) event contains the originating request Id (or the orderId in case the error was raised when placing an order), a numeric error code and a brief description. It is important to keep in mind that this function is used for *true* error messages as well as notifications that do not mean anything is wrong.

**API Error Messages when TWS is not set to the English Language**

- Currently on the Windows platform, error messages are sent using Latin1 encoding. If TWS is launched in a non-Western language, it is recommended to enable the setting at Global Configuration -> API -> Settings to "Show API error messages in English".

```
class TestWrapper(wrapper.EWrapper):
...
		def error(self, reqId: TickerId, errorCode: int, errorString: str, advancedOrderRejectJson = ""):
				super().error(reqId, errorCode, errorString, advancedOrderRejectJson)
				if advancedOrderRejectJson:
						print("Error. Id:", reqId, "Code:", errorCode, "Msg:", errorString, "AdvancedOrderRejectJson:", advancedOrderRejectJson)
				else:
						print("Error. Id:", reqId, "Code:", errorCode, "Msg:", errorString)
```

# Message Codes

The TWS uses the [IBApi.EWrapper.error](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a7dfc221702ca65195609213c984729b8) method not only to deliver errors but also warnings or informative messages. This is done mostly for simplicity's sake. Below is a table with all the messages which can be sent by the TWS/IB Gateway. All messages delivered by the TWS are usually accompanied by a brief but meaningful description pointing in the direction of the problem.

## System Message Codes

Remember that the TWS API simply connects to a running TWS/IB Gateway which most of times will be running on your local network if not in the same host as the client application. It is your responsibility to provide reliable connectivity between the TWS and your client application.

The messages in the table below are not a consequence of any action performed by the client application. They are notifications about the connectivity status between the TWS and itself and our servers. Your client application must pay special attention to them and handle the situation accordingly. You are very likely to loose connectivity to our servers at least once a day due to our daily server maintenance downtime as clearly detailed in our [Current System Status](https://www.interactivebrokers.com/en/?f=%2Fen%2Fsoftware%2FsystemStatus.php) page. Note that after the system reset, the TWS/IB Gateway will automatically reconnect to our servers and you can resume your operations normally.

**Important:** during a reset period, there may be an interruption in the ability to log in or manage orders. Existing orders (native types) will operate normally although execution reports and simulated orders will be delayed until the reset is complete. It is not recommended to operate during the scheduled reset times.

| Code | TWS message                                                  | Additional notes                                             |
| ---- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 1100 | Connectivity between IB and the TWS has been lost.           | Your TWS/IB Gateway has been disconnected from IB servers. This can occur because of an internet connectivity issue, a nightly reset of the IB servers, or a competing session. |
| 1101 | Connectivity between IB and TWS has been restored- data lost.* | The TWS/IB Gateway has successfully reconnected to IB's servers. Your market data requests have been lost and need to be re-submitted. |
| 1102 | Connectivity between IB and TWS has been restored- data maintained. | The TWS/IB Gateway has successfully reconnected to IB's servers. Your market data requests have been recovered and there is no need for you to re-submit them. |
| 1300 | TWS socket port has been reset and this connection is being dropped. Please reconnect on the new port - <port_num> | The port number in the TWS/IBG settings has been changed during an active API connection. |

## Warning Message Codes

| Code | TWS message                                                  | Additional notes                                             |
| ---- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 2100 | New account data requested from TWS. API client has been unsubscribed from account data. | The TWS only allows one [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) request at a time. If the client application attempts to subscribe to a second account without canceling the previous subscription, the new request will override the old one and the TWS will send this message notifying so. |
| 2101 | Unable to subscribe to account as the following clients are subscribed to a different account. | If a client application invokes [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) when there is an active subscription **started by a different client**, the TWS will reject the new subscription request with this message. |
| 2102 | Unable to modify this order as it is still being processed.  | If you attempt to modify an order before it gets processed by the system, the modification will be rejected. Wait until the order has been fully processed before modifying it. See [Placing Orders](https://interactivebrokers.github.io/tws-api/order_submission.html) for further details. |
| 2103 | A market data farm is disconnected.                          | Indicates a connectivity problem to an IB server. Outside of the nightly IB server reset, this typically indicates an underlying ISP connectivity issue. |
| 2104 | Market data farm connection is OK                            | A notification that connection to the market data server is ok. This is a notification and **not** a true error condition, and is expected on first establishing connection. |
| 2158 | Sec-def data farm connection is OK                           | A notification that connection to the Security definition data server is ok. This is a notification and **not** a true error condition, and is expected on first establishing connection. |
| 2105 | A historical data farm is disconnected.                      | Indicates a connectivity problem to an IB server. Outside of the nightly IB server reset, this typically indicates an underlying ISP connectivity issue. |
| 2106 | A historical data farm is connected.                         | A notification that connection to the market data server is ok. This is a notification and **not** a true error condition, and is expected on first establishing connection. |
| 2107 | A historical data farm connection has become inactive but should be available upon demand. | Whenever a connection to the historical data farm is not being used because there is not an active historical data request, the connection will go inactive in IB Gateway. This does **not** indicate any connectivity issue or problem with IB Gateway. As soon as a historical data request is made the status will change back to active. |
| 2108 | A market data farm connection has become inactive but should be available upon demand. | Whenever a connection to our data farms is not needed, it will become dormant. There is nothing abnormal nor wrong with your client application nor with the TWS. You can safely ignore this message. |
| 2109 | Order Event Warning: Attribute "Outside Regular Trading Hours" is ignored based on the order type and destination. PlaceOrder is now processed. | Indicates the outsideRth flag was set for an order for which there is not a regular vs outside regular trading hour distinction |
| 2110 | Connectivity between TWS and server is broken. It will be restored automatically. | Indicates a connectivity problem between TWS or IBG and the IB server. This will usually only occur during the IB nightly server reset; cases at other times indicate a problem in the local ISP connectivity. |
| 2137 | Cross Side Warning                                           | This warning message occurs in TWS version 955 and higher. It occurs when an order will change the position in an account from long to short or from short to long. To bypass the warning, a new feature has been added to IB Gateway 956 (or higher) and TWS 957 (or higher) so that once can go to Global Configuration > Messages and disable the "Cross Side Warning". |
| 2168 | Etrade Only Not Supported Warning                            | The EtradeOnly [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) attribute is no longer supported. Error received with TWS versions 983+. Remove attribute to place order. |
| 2169 | Firm Quote Only Not Supported Warning                        | The firmQuoteOnly [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) attribute is no longer supported. Error received with TWS versions 983+. Remove attribute to place order. |

## Client Error Codes

Client errors are those occurring purely on the TWS API client code and as such they are never sent by the TWS. They are mostly errors happening when validating messages before these are sent to the TWS and you are unlikely to receive the vast majority of them. As such only a small list of these errors is documented below. To see all available errors of this type please refer to the [IBApi.EClientErrors](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClientErrors.html) class.

| Code           | Message                                                      | Additional notes                                             |
| -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 501            | Already Connected.                                           | Your client application is already connected to the TWS.     |
| 502            | Couldn't connect to TWS. Confirm that "Enable ActiveX and Socket Clients" is enabled and connection port is the same as "Socket Port" on the TWS "Edit->Global Configuration...->API->Settings" menu. | When you receive this error message it is either because you have not enabled API connectivity in the TWS and/or you are trying to connect on the wrong port. Refer to the TWS' API Settings as explained in the error message. See also [Connectivity](https://interactivebrokers.github.io/tws-api/connection.html) |
| 503            | The TWS is out of date and must be upgraded.                 | Indicates TWS or IBG is too old for use with the current API version. Can also be triggered if the TWS version does not support a specific API function. |
| 504            | Not connected.                                               | You are trying to perform a request without properly connecting and/or after connection to the TWS has been broken probably due to an unhandled exception within your client application. |
| WinError 10038 | An operation was attempted on something that is not a socket. | This indicates socket connection was closed improperly. Typically for Python clients. You may refer : https://stackoverflow.com/questions/15210178/python-socket-programming-oserror-winerror-10038-an-operation-was-attempted-o |

## TWS Error Codes

| Code                                                         | TWS message                                                  | Additional notes                                             |
| ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 100                                                          | Max rate of messages per second has been exceeded.           | The client application has exceeded the rate of 50 messages/second. The TWS will likely disconnect the client application after this message. |
| 101                                                          | Max number of tickers has been reached.                      | The current number of active market data subscriptions in TWS and the API altogether has been exceeded. This number is calculated based on a formula which is based on the equity, commissions, and quote booster packs in an account. Active lines can be checked in Tws using the Ctrl-Alt-= combination |
| 102                                                          | Duplicate ticker ID.                                         | A market data request used a ticker ID which is already in use by an active request. |
| 103                                                          | Duplicate order ID.                                          | An order was placed with an order ID that is less than or equal to the order ID of a previous order from this client |
| 104                                                          | Can't modify a filled order.                                 | An attempt was made to modify an order which has already been filled by the system. |
| 105                                                          | Order being modified does not match original order.          | An order was placed with an order ID of a currently open order but basic parameters differed (aside from quantity or price fields) |
| 106                                                          | Can't transmit order ID:                                     |                                                              |
| 107                                                          | Cannot transmit incomplete order.                            | Order is missing a required field.                           |
| 109                                                          | Price is out of the range defined by the Percentage setting at order defaults frame. The order will not be transmitted. | Price entered is outside the range of prices set in TWS or IB Gateway Order Precautionary Settings |
| 110                                                          | The price does not conform to the minimum price variation for this contract. | An entered price field has more digits of precision than is allowed for this particular contract. Minimum increment information can be found on the IB Contracts and Securities Search page. |
| 111                                                          | The TIF (Tif type) and the order type are incompatible.      | The time in force specified cannot be used with this order type. Please refer to order tickets in TWS for allowable combinations. |
| 113                                                          | The Tif option should be set to DAY for MOC and LOC orders.  | Market-on-close or Limit-on-close orders should be sent with time in force set to 'DAY' |
| 114                                                          | Relative orders are valid for stocks only.                   | This error is deprecated.                                    |
| 115                                                          | Relative orders for US stocks can only be submitted to SMART, SMART_ECN, INSTINET, or PRIMEX. | This error is deprecated.                                    |
| 116                                                          | The order cannot be transmitted to a dead exchange.          | Exchange field is invalid.                                   |
| 117                                                          | The block order size must be at least 50.                    |                                                              |
| 118                                                          | VWAP orders must be routed through the VWAP exchange.        |                                                              |
| 119                                                          | Only VWAP orders may be placed on the VWAP exchange.         | When an order is routed to the VWAP exchange, the type of the order must be defined as 'VWAP'. |
| 120                                                          | It is too late to place a VWAP order for today.              | The cutoff has passed for the current day to place VWAP orders. |
| 121                                                          | Invalid BD flag for the order. Check "Destination" and "BD" flag. | This error is deprecated.                                    |
| 122                                                          | No request tag has been found for order:                     |                                                              |
| 123                                                          | No record is available for conid:                            | The specified contract ID cannot be found. This error is deprecated. |
| 124                                                          | No market rule is available for conid:                       |                                                              |
| 125                                                          | Buy price must be the same as the best asking price.         |                                                              |
| 126                                                          | Sell price must be the same as the best bidding price.       |                                                              |
| 129                                                          | VWAP orders must be submitted at least three minutes before the start time. | The start time specified in the VWAP order is less than 3 minutes after when it is placed. |
| 131                                                          | The sweep-to-fill flag and display size are only valid for US stocks routed through SMART, and will be ignored. |                                                              |
| 132                                                          | This order cannot be transmitted without a clearing account. |                                                              |
| 133                                                          | Submit new order failed.                                     |                                                              |
| 134                                                          | Modify order failed.                                         |                                                              |
| 135                                                          | Can't find order with ID =                                   | An attempt was made to cancel an order not currently in the system. |
| 136                                                          | This order cannot be cancelled.                              | An attempt was made to cancel an order than cannot be cancelled, for instance because |
| 137                                                          | VWAP orders can only be cancelled up to three minutes before the start time. |                                                              |
| 138                                                          | Could not parse ticker request:                              |                                                              |
| 139                                                          | Parsing error:                                               | Error in command syntax generated parsing error.             |
| 140                                                          | The size value should be an integer:                         | The size field in the Order class has an invalid type.       |
| 141                                                          | The price value should be a double:                          | A price field in the Order type has an invalid type.         |
| 142                                                          | Institutional customer account does not have account info    |                                                              |
| 143                                                          | Requested ID is not an integer number.                       | The IDs used in API requests must be integer values.         |
| 144                                                          | Order size does not match total share allocation. To adjust the share allocation, right-click on the order and select â€œModify > Share Allocation.â€� |                                                              |
| 145                                                          | Error in validating entry fields -                           | An error occurred with the syntax of a request field.        |
| 146                                                          | Invalid trigger method.                                      | The trigger method specified for a method such as stop or trail stop was not one of the allowable methods. |
| 147                                                          | The conditional contract info is incomplete.                 |                                                              |
| 148                                                          | A conditional order can only be submitted when the order type is set to limit or market. | This error is deprecated.                                    |
| 151                                                          | This order cannot be transmitted without a user name.        | In DDE the user name is a required field in the place order command. |
| 152                                                          | The "hidden" order attribute may not be specified for this order. | The order in question cannot be placed as a hidden order. See- https://www.interactivebrokers.com/en/index.php?f=596 |
| 153                                                          | EFPs can only be limit orders.                               | This error is deprecated.                                    |
| 154                                                          | Orders cannot be transmitted for a halted security.          | A security was halted for trading when an order was placed.  |
| 155                                                          | A sizeOp order must have a user name and account.            | This error is deprecated.                                    |
| 156                                                          | A SizeOp order must go to IBSX                               | This error is deprecated.                                    |
| 157                                                          | An order can be EITHER Iceberg or Discretionary. Please remove either the Discretionary amount or the Display size. | In the Order class extended attributes the fields 'Iceberg' and 'Discretionary' cannot |
| 158                                                          | You must specify an offset amount or a percent offset value. | TRAIL and TRAIL STOP orders must have an absolute offset amount or offset percentage specified. |
| 159                                                          | The percent offset value must be between 0% and 100%.        | A percent offset value was specified outside the allowable range of 0% and 100%. |
| 160                                                          | The size value cannot be zero.                               | The size of an order must be a positive quantity.            |
| 161                                                          | Cancel attempted when order is not in a cancellable state. Order permId = | An attempt was made to cancel an order not active at the time. |
| 162                                                          | Historical market data Service error message.                |                                                              |
| 163                                                          | The price specified would violate the percentage constraint specified in the default order settings. | The order price entered is outside the allowable range specified in the Order Precautionary Settings of TWS or IB Gateway |
| 164                                                          | There is no market data to check price percent violations.   | No market data is available for the specified contract to determine whether the specified price is outside the price percent precautionary order setting. |
| 165                                                          | Historical market Data Service query message.                | There was an issue with a historical data request, such is no such data in IB's database. Note this message is not specific to the API. |
| 166                                                          | HMDS Expired Contract Violation.                             | Historical data is not available for the specified expired contract. |
| 167                                                          | VWAP order time must be in the future.                       | The start time of a VWAP order has already passed.           |
| 168                                                          | Discretionary amount does not conform to the minimum price variation for this contract. | The discretionary field is specified with a number of degrees of precision higher than what is allowed for a specified contract. |
| 200                                                          | No security definition has been found for the request.       | The specified contract does not match any in IB's database, usually because of an incorrect or missing parameter. |
| The contract description specified for <Symbol> is ambiguous | Ambiguity may occur when the contract definition provided is not unique.  For some stocks that has the same Symbol, Currency and Exchange, you need to specify the [IBApi.Contract.PrimaryExch](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Contract.html#ae31aaa76ab07d7ddb72197b4a68aa984) attribute to avoid ambiguity. Please refer to a sample stock contract [here](https://interactivebrokers.github.io/tws-api/basic_contracts.html#stk).  For futures that has multiple multipliers for the same expiration, You need to specify the [IBApi.Contract.Multiplier](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Contract.html#af8c3b969995053ca37a6aea2e94215d2) attribute to avoid ambiguity. Please refer to a sample futures contract [here](https://interactivebrokers.github.io/tws-api/basic_contracts.html#fut). |                                                              |
| 201                                                          | Order rejected - Reason:                                     | An attempted order was rejected by the IB servers. See [Order Placement Considerations](https://interactivebrokers.github.io/tws-api/automated_considerations.html#order_placement) for additional information/considerations for these errors. |
| 202                                                          | Order cancelled - Reason:                                    | An active order on the IB server was cancelled. See [Order Placement Considerations](https://interactivebrokers.github.io/tws-api/automated_considerations.html#order_placement) for additional information/considerations for these errors. |
| 203                                                          | The security <security> is not available or allowed for this account. | The specified security has a trading restriction with a specific account. |
| 300                                                          | Can't find EId with ticker Id:                               | An attempt was made to cancel market data for a ticker ID that was not associated with a current subscription. With the DDE API this occurs by clearing the spreadsheet cell. |
| 301                                                          | Invalid ticker action:                                       |                                                              |
| 302                                                          | Error parsing stop ticker string:                            |                                                              |
| 303                                                          | Invalid action:                                              | An action field was specified that is not available for the account. For most accounts this is only BUY or SELL. Some institutional accounts also have the options SSHORT or SLONG available. |
| 304                                                          | Invalid account value action:                                |                                                              |
| 305                                                          | Request parsing error, the request has been ignored.         | The syntax of a DDE request is invalid.                      |
| 306                                                          | Error processing DDE request:                                | An issue with a DDE request prevented it from processing.    |
| 307                                                          | Invalid request topic:                                       | The 'topic' field in a DDE request is invalid.               |
| 308                                                          | Unable to create the 'API' page in TWS as the maximum number of pages already exists. | An order placed from the API will automatically open a new page in classic TWS, however there are already the maximum number of pages open. |
| 309                                                          | Max number (3) of market depth requests has been reached. Note: TWS currently limits users to a maximum of 3 distinct market depth requests. This same restriction applies to API clients, however API clients may make multiple market depth requests for the same security. |                                                              |
| 310                                                          | Can't find the subscribed market depth with tickerId:        | An attempt was made to cancel market depth for a ticker not currently active. |
| 311                                                          | The origin is invalid.                                       | The origin field specified in the Order class is invalid.    |
| 312                                                          | The combo details are invalid.                               | Combination contract specified has invalid parameters.       |
| 313                                                          | The combo details for leg '<leg number>' are invalid.        | A combo leg was not defined correctly.                       |
| 314                                                          | Security type 'BAG' requires combo leg details.              | When specifying security type as 'BAG' make sure to also add combo legs with details. |
| 315                                                          | Stock combo legs are restricted to SMART order routing.      | Make sure to specify 'SMART' as an exchange when using stock combo contracts. |
| 316                                                          | Market depth data has been HALTED. Please re-subscribe.      | You need to re-subscribe to start receiving market depth data again. |
| 317                                                          | Market depth data has been RESET. Please empty deep book contents before applying any new entries. |                                                              |
| 319                                                          | Invalid log level <log level>                                | Make sure that you are setting a log level to a value in range of 1 to 5. |
| 320                                                          | Server error when reading an API client request.             |                                                              |
| 321                                                          | Server error when validating an API client request.          |                                                              |
| 322                                                          | Server error when processing an API client request.          |                                                              |
| 323                                                          | Server error: cause - s                                      |                                                              |
| 324                                                          | Server error when reading a DDE client request (missing information). | Make sure that you have specified all the needed information for your request. |
| 325                                                          | Discretionary orders are not supported for this combination of exchange and order type. | Make sure that you are specifying a valid combination of exchange and order type for the discretionary order. |
| 326                                                          | Unable to connect as the client id is already in use. Retry with a unique client id. | Another client application is already connected with the specified client id. |
| 327                                                          | Only API connections with clientId set to 0 can set the auto bind TWS orders property. |                                                              |
| 328                                                          | Trailing stop orders can be attached to limit or stop-limit orders only. | Indicates attempt to attach trail stop to order which was not a limit or stop-limit. |
| 329                                                          | Order modify failed. Cannot change to the new order type.    | You are not allowed to modify initial order type to the specific order type you are using. |
| 330                                                          | Only FA or STL customers can request managed accounts list.  | Make sure that your account type is either FA or STL.        |
| 331                                                          | Internal error. FA or STL does not have any managed accounts. | You do not have any managed accounts.                        |
| 332                                                          | The account codes for the order profile are invalid.         | You need to check that the account codes you specified for your request are valid. |
| 333                                                          | Invalid share allocation syntax.                             |                                                              |
| 334                                                          | Invalid Good Till Date order                                 | Check you order settings.                                    |
| 335                                                          | Invalid delta: The delta must be between 0 and 100.          |                                                              |
| 336                                                          | The time or time zone is invalid. The correct format is hh:mm:ss xxx where xxx is an optionally specified time-zone. E.g.: 15:59:00 EST Note that there is a space between the time and the time zone. If no time zone is specified, local time is assumed. |                                                              |
| 337                                                          | The date, time, or time-zone entered is invalid. The correct format is yyyymmdd hh:mm:ss xxx where yyyymmdd and xxx are optional. E.g.: 20031126 15:59:00 ESTNote that there is a space between the date and time, and between the time and time-zone. |                                                              |
| 338                                                          | Good After Time orders are currently disabled on this exchange. |                                                              |
| 339                                                          | Futures spread are no longer supported. Please use combos instead. |                                                              |
| 340                                                          | Invalid improvement amount for box auction strategy.         |                                                              |
| 341                                                          | Invalid delta. Valid values are from 1 to 100. You can set the delta from the "Pegged to Stock" section of the Order Ticket Panel, or by selecting Page/Layout from the main menu and adding the Delta column. |                                                              |
| 342                                                          | Pegged order is not supported on this exchange.              | You can review all order types and supported exchanges on the [Order Types and Algos](https://www.interactivebrokers.co.uk/en/index.php?f=4985) page. |
| 343                                                          | The date, time, or time-zone entered is invalid. The correct format is yyyymmdd hh:mm:ss xxx |                                                              |
| 344                                                          | The account logged into is not a financial advisor account.  | You are trying to perform an action that is only available for the financial advisor account. |
| 345                                                          | Generic combo is not supported for FA advisor account.       |                                                              |
| 346                                                          | Not an institutional account or an away clearing account.    |                                                              |
| 347                                                          | Short sale slot value must be 1 (broker holds shares) or 2 (delivered from elsewhere). | Make sure that your slot value is either 1 or 2.             |
| 348                                                          | Order not a short sale – type must be SSHORT to specify short sale slot. | Make sure that the action you specified is 'SSHORT'.         |
| 349                                                          | Generic combo does not support "Good After" attribute.       |                                                              |
| 350                                                          | Minimum quantity is not supported for best combo order.      |                                                              |
| 351                                                          | The "Regular Trading Hours only" flag is not valid for this order. |                                                              |
| 352                                                          | Short sale slot value of 2 (delivered from elsewhere) requires location. | You need to specify designatedLocation for your order.       |
| 353                                                          | Short sale slot value of 1 requires no location be specified. | You do not need to specify designatedLocation for your order. |
| 354                                                          | Not subscribed to requested market data.                     | You do not have live market data available in your account for the specified instruments. For further details please refer to [Streaming Market Data](https://interactivebrokers.github.io/tws-api/market_data.html). |
| 355                                                          | Order size does not conform to market rule.                  | Check order size parameters for the specified contract from the TWS Contract Details. |
| 356                                                          | Smart-combo order does not support OCA group.                | Remove OCA group from your order.                            |
| 357                                                          | Your client version is out of date.                          |                                                              |
| 358                                                          | Smart combo child order not supported.                       |                                                              |
| 359                                                          | Combo order only supports reduce on fill without block(OCA). |                                                              |
| 360                                                          | No whatif check support for smart combo order.               | Pre-trade commissions and margin information is not available for this type of order. |
| 361                                                          | Invalid trigger price.                                       |                                                              |
| 362                                                          | Invalid adjusted stop price.                                 |                                                              |
| 363                                                          | Invalid adjusted stop limit price.                           |                                                              |
| 364                                                          | Invalid adjusted trailing amount.                            |                                                              |
| 365                                                          | No scanner subscription found for ticker id:                 | Scanner market data subscription request with this ticker id has either been cancelled or is not found. |
| 366                                                          | No historical data query found for ticker id:                | Historical market data request with this ticker id has either been cancelled or is not found. |
| 367                                                          | Volatility type if set must be 1 or 2 for VOL orders. Do not set it for other order types. |                                                              |
| 368                                                          | Reference Price Type must be 1 or 2 for dynamic volatility management. Do not set it for non-VOL orders. |                                                              |
| 369                                                          | Volatility orders are only valid for US options.             | Make sure that you are placing an order for US OPT contract. |
| 370                                                          | Dynamic Volatility orders must be SMART routed, or trade on a Price Improvement Exchange. |                                                              |
| 371                                                          | VOL order requires positive floating point value for volatility. Do not set it for other order types. |                                                              |
| 372                                                          | Cannot set dynamic VOL attribute on non-VOL order.           | Make sure that your order type is 'VOL'.                     |
| 373                                                          | Can only set stock range attribute on VOL or RELATIVE TO STOCK order. |                                                              |
| 374                                                          | If both are set, the lower stock range attribute must be less than the upper stock range attribute. |                                                              |
| 375                                                          | Stock range attributes cannot be negative.                   |                                                              |
| 376                                                          | The order is not eligible for continuous update. The option must trade on a cheap-to-reroute exchange. |                                                              |
| 377                                                          | Must specify valid delta hedge order aux. price.             |                                                              |
| 378                                                          | Delta hedge order type requires delta hedge aux. price to be specified. | Make sure your order has delta attribute.                    |
| 379                                                          | Delta hedge order type requires that no delta hedge aux. price be specified. | Make sure you do not specify aux. delta hedge price.         |
| 380                                                          | This order type is not allowed for delta hedge orders.       | Limit, Market or Relative orders are supported.              |
| 381                                                          | Your DDE.dll needs to be upgraded.                           |                                                              |
| 382                                                          | The price specified violates the number of ticks constraint specified in the default order settings. |                                                              |
| 383                                                          | The size specified violates the size constraint specified in the default order settings. |                                                              |
| 384                                                          | Invalid DDE array request.                                   |                                                              |
| 385                                                          | Duplicate ticker ID for API scanner subscription.            | Make sure you are using a unique ticker ID for your new scanner subscription. |
| 386                                                          | Duplicate ticker ID for API historical data query.           | Make sure you are using a unique ticker ID for your new historical market data query. |
| 387                                                          | Unsupported order type for this exchange and security type.  | You can review all order types and supported exchanges on the [Order Types and Algos](https://www.interactivebrokers.co.uk/en/index.php?f=4985) page. |
| 388                                                          | Order size is smaller than the minimum requirement.          | Check order size parameters for the specified contract from the TWS Contract Details. |
| 389                                                          | Supplied routed order ID is not unique.                      |                                                              |
| 390                                                          | Supplied routed order ID is invalid.                         |                                                              |
| 391                                                          | The time or time-zone entered is invalid. The correct format is hh:mm:ss xxx |                                                              |
| 392                                                          | Invalid order: contract expired.                             | You can not place an order for the expired contract.         |
| 393                                                          | Short sale slot may be specified for delta hedge orders only. |                                                              |
| 394                                                          | Invalid Process Time: must be integer number of milliseconds between 100 and 2000. Found: |                                                              |
| 395                                                          | Due to system problems, orders with OCA groups are currently not being accepted. | Check TWS bulletins for more information.                    |
| 396                                                          | Due to system problems, application is currently accepting only Market and Limit orders for this contract. | Check TWS bulletins for more information.                    |
| 397                                                          | Due to system problems, application is currently accepting only Market and Limit orders for this contract. |                                                              |
| 398                                                          | < > cannot be used as a condition trigger.                   | Please make sure that you specify a valid condition          |
| 399                                                          | Order message error                                          |                                                              |
| 400                                                          | Algo order error.                                            |                                                              |
| 401                                                          | Length restriction.                                          |                                                              |
| 402                                                          | Conditions are not allowed for this contract.                | Condition order type does not support for this contract      |
| 403                                                          | Invalid stop price.                                          | The Stop Price you specified for the order is invalid for the contract |
| 404                                                          | Shares for this order are not immediately available for short sale. The order will be held while we attempt to locate the shares. | You order is held by the TWS because you are trying to sell a contract but you do not have any long position and the market does not have short sale available. You order will be transmitted once there is short sale available on the market |
| 405                                                          | The child order quantity should be equivalent to the parent order size. | This error is deprecated.                                    |
| 406                                                          | The currency < > is not allowed.                             | Please specify a valid currency                              |
| 407                                                          | The symbol should contain valid non-unicode characters only. | Please check your contract Symbol                            |
| 408                                                          | Invalid scale order increment.                               |                                                              |
| 409                                                          | Invalid scale order. You must specify order component size.  | ScaleInitLevelSize specified is invalid                      |
| 410                                                          | Invalid subsequent component size for scale order.           | ScaleSubsLevelSize specified is invalid                      |
| 411                                                          | The "Outside Regular Trading Hours" flag is not valid for this order. | Trading outside of regular trading hours is not available for this security |
| 412                                                          | The contract is not available for trading.                   |                                                              |
| 413                                                          | What-if order should have the transmit flag set to true.     | You need to set [IBApi.Order.Transmit](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#aaa1c4f31b9580ee0715edcd78a51cbec) to TRUE |
| 414                                                          | Snapshot market data subscription is not applicable to generic ticks. | You must leave Generic Tick List to be empty when requesting snapshot market data |
| 415                                                          | Wait until previous RFQ finishes and try again.              |                                                              |
| 416                                                          | RFQ is not applicable for the contract. Order ID:            |                                                              |
| 417                                                          | Invalid initial component size for scale order.              | ScaleInitLevelSize specified is invalid                      |
| 418                                                          | Invalid scale order profit offset.                           | ScaleProfitOffset specified is invalid                       |
| 419                                                          | Missing initial component size for scale order.              | You need to specify the ScaleInitLevelSize                   |
| 420                                                          | Invalid real-time query.                                     | [Information about pacing violations](http://interactivebrokers.github.io/tws-api/historical_limitations.html#pacing_violations) |
| 421                                                          | Invalid route.                                               | This error is deprecated.                                    |
| 422                                                          | The account and clearing attributes on this order may not be changed. |                                                              |
| 423                                                          | Cross order RFQ has been expired. THI committed size is no longer available. Please open order dialog and verify liquidity allocation. |                                                              |
| 424                                                          | FA Order requires allocation to be specified.                | This error is deprecated.                                    |
| 425                                                          | FA Order requires per-account manual allocations because there is no common clearing instruction. Please use order dialog Adviser tab to enter the allocation. | This error is deprecated.                                    |
| 426                                                          | None of the accounts have enough shares.                     | You are not able to enter short position with Cash Account   |
| 427                                                          | Mutual Fund order requires monetary value to be specified.   | This error is deprecated.                                    |
| 428                                                          | Mutual Fund Sell order requires shares to be specified.      | This error is deprecated.                                    |
| 429                                                          | Delta neutral orders are only supported for combos (BAG security type). |                                                              |
| 430                                                          | We are sorry, but fundamentals data for the security specified is not available. |                                                              |
| 431                                                          | What to show field is missing or incorrect.                  | This error is deprecated.                                    |
| 432                                                          | Commission must not be negative.                             | This error is deprecated.                                    |
| 433                                                          | Invalid "Restore size after taking profit" for multiple account allocation scale order. |                                                              |
| 434                                                          | The order size cannot be zero.                               |                                                              |
| 435                                                          | You must specify an account.                                 | The function you invoked only works on a single account      |
| 436                                                          | You must specify an allocation (either a single account, group, or profile). | When you try to place an order with a Financial Advisor account, you must specify the order to be routed to either a single account, a group, or a profile. |
| 437                                                          | Order can have only one flag Outside RTH or Allow PreOpen.   | This error is deprecated.                                    |
| 438                                                          | The application is now locked.                               | This error is deprecated.                                    |
| 439                                                          | Order processing failed. Algorithm definition not found.     | Please double check your specification for [IBApi.Order.AlgoStrategy](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a10f99918316ed4096736fff066c21192) and [IBApi.Order.AlgoParams](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a407a5371e7d074d5dfeb5922ecbc88b3) |
| 440                                                          | Order modify failed. Algorithm cannot be modified.           |                                                              |
| 441                                                          | Algo attributes validation failed:                           | Please double check your specification for [IBApi.Order.AlgoStrategy](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a10f99918316ed4096736fff066c21192) and [IBApi.Order.AlgoParams](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a407a5371e7d074d5dfeb5922ecbc88b3) |
| 442                                                          | Specified algorithm is not allowed for this order.           |                                                              |
| 443                                                          | Order processing failed. Unknown algo attribute.             | Specification for [IBApi.Order.AlgoParams](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html#a407a5371e7d074d5dfeb5922ecbc88b3) is incorrect |
| 444                                                          | Volatility Combo order is not yet acknowledged. Cannot submit changes at this time. | The order is not in a state that is able to be modified      |
| 445                                                          | The RFQ for this order is no longer valid.                   |                                                              |
| 446                                                          | Missing scale order profit offset.                           | ScaleProfitOffset is not properly specified                  |
| 447                                                          | Missing scale price adjustment amount or interval.           | ScalePriceAdjustValue or ScalePriceAdjustInterval is not specified properly |
| 448                                                          | Invalid scale price adjustment interval.                     | ScalePriceAdjustInterval specified is invalid                |
| 449                                                          | Unexpected scale price adjustment amount or interval.        | ScalePriceAdjustValue or ScalePriceAdjustInterval specified is invalid |
| 507                                                          | Bad Message Length (Java-only)                               | Indicates EOF exception was caught while reading from the socket. This can occur if there is an attempt to connect to TWS with a client ID that is already in use, or if TWS is locked, closes, or breaks the connection. It should be handled by the client application and used to indicate that the socket connection is not valid. |
| 10000                                                        | Cross currency combo error.                                  |                                                              |
| 10001                                                        | Cross currency vol error.                                    |                                                              |
| 10002                                                        | Invalid non-guaranteed legs.                                 |                                                              |
| 10003                                                        | IBSX not allowed.                                            |                                                              |
| 10005                                                        | Read-only models.                                            |                                                              |
| 10006                                                        | Missing parent order.                                        | The parent order ID specified cannot be found. In some cases this can occur with bracket orders if the child order is placed immediately after the parent order; a brief pause of 50 ms or less will be necessary before the child order is transmitted to TWS/IBG. |
| 10007                                                        | Invalid hedge type.                                          |                                                              |
| 10008                                                        | Invalid beta value.                                          |                                                              |
| 10009                                                        | Invalid hedge ratio.                                         |                                                              |
| 10010                                                        | Invalid delta hedge order.                                   |                                                              |
| 10011                                                        | Currency is not supported for Smart combo.                   |                                                              |
| 10012                                                        | Invalid allocation percentage                                | FaPercentage specified is not valid                          |
| 10013                                                        | Smart routing API error (Smart routing opt-out required).    |                                                              |
| 10014                                                        | PctChange limits.                                            | This error is deprecated                                     |
| 10015                                                        | Trading is not allowed in the API.                           |                                                              |
| 10016                                                        | Contract is not visible.                                     | This error is deprecated                                     |
| 10017                                                        | Contracts are not visible.                                   | This error is deprecated                                     |
| 10018                                                        | Orders use EV warning.                                       |                                                              |
| 10019                                                        | Trades use EV warning.                                       |                                                              |
| 10020                                                        | Display size should be smaller than order size./td>          | The display size should be smaller than the total quantity   |
| 10021                                                        | Invalid leg2 to Mkt Offset API.                              | This error is deprecated                                     |
| 10022                                                        | Invalid Leg Prio API.                                        | This error is deprecated                                     |
| 10023                                                        | Invalid combo display size API.                              | This error is deprecated                                     |
| 10024                                                        | Invalid don't start next legin API.                          | This error is deprecated                                     |
| 10025                                                        | Invalid leg2 to Mkt time1 API.                               | This error is deprecated                                     |
| 10026                                                        | Invalid leg2 to Mkt time2 API.                               | This error is deprecated                                     |
| 10027                                                        | Invalid combo routing tag API.                               | This error is deprecated                                     |
| 10090                                                        | Part of requested market data is not subscribed.             | Indicates that some tick types requested require additional market data subscriptions not held in the account. This commonly occurs for instance if a user has options subscriptions but not the underlying stock so the system cannot calculate the real time greek values (other default ticks will be returned). Or alternatively, if generic tick types are specified in a market data request without the associated subscriptions. |
| 10148                                                        | OrderId <OrderId> that needs to be cancelled can not be cancelled, state: | An attempt was made to cancel an order that had already been filled by the system. |
| 10186                                                        | Requested market data is not subscribed. Delayed market data is not enabled | See [Market Data Types](https://interactivebrokers.github.io/tws-api/market_data_type.html) on how to enable delayed data. |
| 10197                                                        | No market data during competing session                      | Indicates that the user is logged into the paper account and live account simultaneously trying to request live market data using both the accounts. In such a scenario preference would be given to the live account, for more details please refer: https://ibkr.info/node/1719 |
| 10225                                                        | Bust event occurred, current subscription is deactivated. Please resubscribe real-time bars immediately |                                                              |
| 10230                                                        | You have unsaved FA changes. Please retry 'request FA' operation later, when 'replace FA' operation is complete | There are pending Financial Advisor configuration changes. See [Financial Advisors](https://interactivebrokers.github.io/tws-api/financial_advisor.html) |
| 10231                                                        | The following Groups and/or Profiles contain invalid accounts: <list of groups/profiles> | If the account(s) inside Groups or Profiles is/are incorrect in xml-formatted configuration string of replaceFA request, then the error shows list of such Groups and/or Profiles. |
| 10233                                                        | Defaults were inherited from CASH preset during the creation of this order. |                                                              |
| 10234                                                        | The Decision Maker field is required and not set for this order (non-desktop). |                                                              |
| 10235                                                        | The Decision Maker field is required and not set for this order (ibbot). |                                                              |
| 10236                                                        | Child has to be AON if parent order is AON                   |                                                              |
| 10237                                                        | All or None ticket can route entire unfilled size only       |                                                              |
| 10238                                                        | Some error occured during communication with Advisor Setup web-app |                                                              |
| 10239                                                        | This order will affect one or more accounts that are flagged because they do not fit the required risk score criteria prescribed by the group/profile/model allocation. |                                                              |
| 10240                                                        | You must enter a valid Price Cap.                            |                                                              |
| 10241                                                        | Order Quantity is expressed in monetary terms. Modification is not supported via API. Please use desktop version to revise this order. |                                                              |
| 10242                                                        | Fractional-sized order cannot be modified via API. Please use desktop version to revise this order. |                                                              |
| 10243                                                        | Fractional-sized order cannot be placed via API. Please use desktop version to place this order. |                                                              |
| 10244                                                        | Cash Quantity cannot be used for this order                  |                                                              |
| 10245                                                        | This financial instrument does not support fractional shares trading |                                                              |
| 10246                                                        | This order doesn't support fractional shares trading         |                                                              |
| 10247                                                        | Only IB SmartRouting supports fractional shares              |                                                              |
| 10248                                                        | <Account> doesn't have permission to trade fractional shares |                                                              |
| 10249                                                        | <Order type>=""> order doesn't support fractional shares     |                                                              |
| 10250                                                        | The size does not conform to the minimum variation of for this contract |                                                              |
| 10251                                                        | Fractional shares are not supported for allocation orders    |                                                              |
| 10252                                                        | This non-close-position order doesn't support fractional shares trading |                                                              |
| 10253                                                        | Clear Away orders are not supported for multi-leg combo with attached hedge. |                                                              |
| 10254                                                        | Invalid Order: bond expired                                  |                                                              |
| 10268                                                        | The 'EtradeOnly' order attribute is not supported            | The EtradeOnly [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) attribute is no longer supported. Error received with TWS versions 983+ |
| 10269                                                        | The 'firmQuoteOnly' order attribute is not supported         | The firmQuoteOnly [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) attribute is no longer supported. Error received with TWS versions 983+ |
| 10270                                                        | The 'nbboPriceCap' order attribute is not supported          | The nbboPriceCap [IBApi.Order](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Order.html) attribute is no longer supported. Error received with TWS versions 983+ |
| 10276                                                        | News feed is not allowed                                     | The API client is not permissioned for receiving WSH news feed. |
| 10277                                                        | News feed permissions required                               | The API client is not subscribed to receive WSH news feed    |
| 10278                                                        | Duplicate WSH metadata request                               | A request is already pending for the same API client.        |
| 10279                                                        | Failed request WSH metadata                                  | A general error occurred when processing the request.        |
| 10280                                                        | Failed cancel WSH metadata                                   | A general error occurred when processing the request.        |
| 10281                                                        | Duplicate WSH event data request                             | A request is already pending for the same API client.        |
| 10282                                                        | WSH metadata not requested                                   | WSH metadata was not requested by first sending a reqWshMetaData request. |
| 10283                                                        | Fail request WSH event data                                  | A general error occurred when processing the request.        |
| 10284                                                        | Fail cancel WSH event data                                   | A general error occurred when processing the request.        |