"""
Interactive Brokers MCP Server

This server implements the Model Context Protocol (MCP) to provide
Claude Desktop with access to Interactive Brokers' TWS API through
the existing IBKRService.
"""
from mcp.server.fastmcp import FastMCP, Context, Image
import asyncio
import os
import matplotlib.pyplot as plt
import io
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
import json
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Import the existing service
from ibkr_mcp_server.app.services.ibkr_service import (
    IBKRService,
    IBKRConnectionError,
    IBKRTimeoutError,
    IBKRDataError
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), '../../../ibkr_mcp_server.log'))
    ]
)
logger = logging.getLogger('ibkr-mcp')

# Create MCP server
mcp = FastMCP(
    "IBKR Trading Assistant",
    dependencies=["ibapi", "matplotlib", "tenacity"]
)

# Create service instance
ibkr_service = IBKRService()

# Connection tool
@mcp.tool()
async def connect_to_tws(host: str = "127.0.0.1", port: int = None, client_id: int = 0) -> Dict:
    """
    Connect to Interactive Brokers TWS or Gateway

    Args:
        host: The hostname or IP address of the TWS/Gateway (default: 127.0.0.1)
        port: The port number (7496 for live trading, 7497 for paper trading)
        client_id: A unique client ID (default: 0)

    Returns:
        A dictionary with connection status
    """
    if port is None:
        logger.error("Port is required but not provided")
        return {"status": "error", "message": "Port is required (7496 for live, 7497 for paper)"}

    try:
        # Override service connection parameters
        ibkr_service.host = host
        ibkr_service.port = port
        ibkr_service.client_id = client_id

        # Connect using the service
        logger.info(f"Attempting to connect to TWS at {host}:{port} with client ID {client_id}")
        await ibkr_service.connect()

        # Get connection time in readable format
        conn_time = ibkr_service.connection_time.strftime("%Y-%m-%d %H:%M:%S") if ibkr_service.connection_time else "Unknown"

        return {
            "status": "connected",
            "message": f"Connected to TWS at {host}:{port}",
            "connectionTime": conn_time,
            "clientId": client_id
        }
    except IBKRTimeoutError as e:
        logger.error(f"Connection timeout: {str(e)}")
        return {
            "status": "error",
            "message": f"Connection timeout: {str(e)}",
            "errorType": "timeout",
            "recommendation": "Check if TWS/Gateway is running and accepting connections"
        }
    except IBKRConnectionError as e:
        logger.error(f"Connection error: {str(e)}")
        return {
            "status": "error",
            "message": f"Connection error: {str(e)}",
            "errorType": "connection",
            "recommendation": "Verify TWS/Gateway is running and properly configured"
        }
    except Exception as e:
        logger.error(f"Unexpected error during connection: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to connect: {str(e)}",
            "errorType": "unknown"
        }

# Portfolio tool
@mcp.tool()
async def get_portfolio() -> Dict:
    """
    Get the current portfolio positions

    Returns:
        A dictionary with portfolio positions
    """
    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get portfolio data using the service
        logger.info("Fetching portfolio data")
        portfolio_data = await ibkr_service.get_portfolio()

        # Add timestamp to the response
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return {
            "status": "success",
            "data": portfolio_data,
            "timestamp": timestamp,
            "count": len(portfolio_data)
        }
    except IBKRConnectionError as e:
        logger.error(f"Connection error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "connection",
            "message": f"Connection error: {str(e)}",
            "recommendation": "Please reconnect to TWS/Gateway"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "timeout",
            "message": f"Timeout error: {str(e)}",
            "recommendation": "Check if TWS/Gateway is responsive"
        }
    except IBKRDataError as e:
        logger.error(f"Data error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "data",
            "message": f"Data error: {str(e)}",
            "recommendation": "Verify account has positions and permissions"
        }
    except Exception as e:
        logger.error(f"Unexpected error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "unknown",
            "message": f"Failed to get portfolio: {str(e)}"
        }

# Market data tool
@mcp.tool()
async def get_market_data(symbol: str, exchange: str = "SMART") -> Dict:
    """
    Get market data for a symbol

    Args:
        symbol: The ticker symbol
        exchange: The exchange (default: SMART)

    Returns:
        A dictionary with market data
    """
    if not symbol:
        logger.error("Symbol is required but not provided")
        return {
            "status": "error",
            "message": "Symbol is required"
        }

    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for market data request: {symbol}")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get market data using the service
        # This assumes your service has a get_market_data method
        # Modify as needed to match your actual service API
        logger.info(f"Fetching market data for {symbol} on {exchange}")
        market_data = await ibkr_service.get_market_data(symbol, exchange)

        # Add timestamp to the response
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return {
            "status": "success",
            "data": market_data,
            "symbol": symbol,
            "exchange": exchange,
            "timestamp": timestamp
        }
    except IBKRConnectionError as e:
        logger.error(f"Connection error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "connection",
            "message": f"Connection error: {str(e)}",
            "recommendation": "Please reconnect to TWS/Gateway"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "timeout",
            "message": f"Timeout error: {str(e)}",
            "recommendation": "Check if TWS/Gateway is responsive"
        }
    except IBKRDataError as e:
        logger.error(f"Data error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "data",
            "message": f"Data error: {str(e)}",
            "recommendation": "Verify symbol exists and you have market data permissions"
        }
    except Exception as e:
        logger.error(f"Unexpected error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "unknown",
            "message": f"Failed to get market data: {str(e)}"
        }

# Add a prompt for Claude Desktop
@mcp.prompt()
def ibkr_trading_prompt() -> str:
    """
    Prompt for interacting with Interactive Brokers
    """
    return """
    You are an Interactive Brokers trading assistant. You can help with:

    1. Connecting to TWS or IB Gateway
    2. Retrieving portfolio information
    3. Getting market data
    4. Analyzing trading opportunities

    To get started, help the user connect to TWS using the connect_to_tws tool.
    For paper trading, use port 7497. For live trading, use port 7496.

    Always provide clear explanations of market data and portfolio information.
    """
# Advanced order types based on Orders.md
@mcp.tool()
async def place_auction_order(symbol: str, action: str, quantity: float, price: float, exchange: str = "SMART") -> Dict:
    """
    Place an Auction order

    An Auction order is entered during pre-market for execution at the Calculated Opening Price (COP).

    Args:
        symbol: The ticker symbol
        action: "BUY" or "SELL"
        quantity: Number of shares/contracts
        price: Limit price
        exchange: Exchange (default: SMART)

    Returns:
        Order status information
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # Create an auction order using your service
        # This assumes your service has a create_order method that accepts order parameters
        order_params = {
            "symbol": symbol,
            "action": action,
            "quantity": quantity,
            "price": price,
            "exchange": exchange,
            "orderType": "MTL",
            "tif": "AUC"
        }

        result = await ibkr_service.create_order(**order_params)
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"Order error: {str(e)}")
        return {"status": "error", "message": f"Failed to place auction order: {str(e)}"}

@mcp.tool()
async def place_adaptive_order(symbol: str, action: str, quantity: float, price: float,
                              priority: str = "Normal", exchange: str = "SMART") -> Dict:
    """
    Place an Adaptive Algorithm order

    The Adaptive Algo combines IB's Smartrouting with user-defined priority settings
    for better execution prices on average than regular limit or market orders.

    Args:
        symbol: The ticker symbol
        action: "BUY" or "SELL"
        quantity: Number of shares/contracts
        price: Limit price
        priority: Priority setting ("Urgent", "Normal", or "Patient")
        exchange: Exchange (default: SMART)

    Returns:
        Order status information
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # Create an adaptive order using your service
        order_params = {
            "symbol": symbol,
            "action": action,
            "quantity": quantity,
            "price": price,
            "exchange": exchange,
            "orderType": "LMT",
            "algoStrategy": "Adaptive",
            "algoParams": [{"tag": "adaptivePriority", "value": priority}]
        }

        result = await ibkr_service.create_order(**order_params)
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"Order error: {str(e)}")
        return {"status": "error", "message": f"Failed to place adaptive order: {str(e)}"}

@mcp.tool()
async def place_bracket_order(symbol: str, action: str, quantity: float, entry_price: float,
                             profit_price: float, stop_price: float, exchange: str = "SMART") -> Dict:
    """
    Place a Bracket Order

    Bracket Orders help limit loss and lock in profit by "bracketing" an order with
    two opposite-side orders. A BUY order is bracketed by a high-side sell limit order
    and a low-side sell stop order.

    Args:
        symbol: The ticker symbol
        action: "BUY" or "SELL"
        quantity: Number of shares/contracts
        entry_price: Entry order price
        profit_price: Profit target price
        stop_price: Stop loss price
        exchange: Exchange (default: SMART)

    Returns:
        Order status information
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # Create a bracket order using your service
        # This assumes your service has a create_bracket_order method
        result = await ibkr_service.create_bracket_order(
            symbol=symbol,
            action=action,
            quantity=quantity,
            entry_price=entry_price,
            profit_price=profit_price,
            stop_price=stop_price,
            exchange=exchange
        )

        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"Order error: {str(e)}")
        return {"status": "error", "message": f"Failed to place bracket order: {str(e)}"}

@mcp.tool()
async def get_account_summary(tags: List[str] = None) -> Dict:
    """
    Get account summary information

    Args:
        tags: List of account tags to retrieve (default: all tags)

    Returns:
        Account summary information
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # If no tags specified, use all tags
        if tags is None:
            tags = ["NetLiquidation", "TotalCashValue", "SettledCash",
                    "AccruedCash", "BuyingPower", "EquityWithLoanValue",
                    "PreviousDayEquityWithLoanValue", "GrossPositionValue",
                    "RegTMargin", "InitMarginReq", "MaintMarginReq",
                    "AvailableFunds", "ExcessLiquidity", "Cushion",
                    "FullInitMarginReq", "FullMaintMarginReq", "FullAvailableFunds",
                    "FullExcessLiquidity", "LookAheadNextChange",
                    "LookAheadInitMarginReq", "LookAheadMaintMarginReq",
                    "LookAheadAvailableFunds", "LookAheadExcessLiquidity",
                    "HighestSeverity", "DayTradesRemaining", "Leverage"]

        # Get account summary using the service
        result = await ibkr_service.get_account_summary(tags)
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"Account summary error: {str(e)}")
        return {"status": "error", "message": f"Failed to get account summary: {str(e)}"}

@mcp.tool()
async def get_pnl_data(account_id: str = None, contract_id: int = None) -> Dict:
    """
    Get P&L data for an account or specific position

    Args:
        account_id: Account ID (default: use the connected account)
        contract_id: Contract ID for position-specific P&L (optional)

    Returns:
        P&L information
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # Get P&L data using the service
        if contract_id:
            # Position-specific P&L
            result = await ibkr_service.get_position_pnl(account_id, contract_id)
        else:
            # Account-level P&L
            result = await ibkr_service.get_account_pnl(account_id)

        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"P&L data error: {str(e)}")
        return {"status": "error", "message": f"Failed to get P&L data: {str(e)}"}

@mcp.tool()
async def search_contracts(pattern: str) -> Dict:
    """
    Search for stock contracts matching a pattern

    The input can be either the first few letters of the ticker symbol
    or a character sequence matching a word in the security name.

    Args:
        pattern: Search pattern (e.g., "AAPL" or "Apple")

    Returns:
        List of matching contracts
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # Search for contracts using the service
        result = await ibkr_service.search_contracts(pattern)
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"Contract search error: {str(e)}")
        return {"status": "error", "message": f"Failed to search contracts: {str(e)}"}

# MCP Resources for data access

# Base portfolio resource (all positions)
@mcp.resource("ibkr://portfolio")
async def portfolio_resource() -> Dict:
    """
    Resource providing all portfolio positions
    """
    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for portfolio resource")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get portfolio data
        logger.info("Fetching portfolio data for resource")
        portfolio = await ibkr_service.get_portfolio()

        # Format for display
        formatted_portfolio = []
        for position in portfolio:
            formatted_portfolio.append({
                "symbol": position.get("symbol", ""),
                "position": position.get("position", 0),
                "marketPrice": position.get("marketPrice", 0),
                "marketValue": position.get("marketValue", 0),
                "averageCost": position.get("averageCost", 0),
                "unrealizedPNL": position.get("unrealizedPNL", 0),
                "realizedPNL": position.get("realizedPNL", 0)
            })

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return {
            "positions": formatted_portfolio,
            "timestamp": timestamp,
            "count": len(formatted_portfolio),
            "status": "success"
        }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in portfolio resource: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in portfolio resource: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Portfolio resource error: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Parameterized portfolio resource (specific position)
@mcp.resource("ibkr://portfolio/{symbol}")
async def portfolio_position_resource(symbol: str) -> Dict:
    """
    Resource providing data for a specific portfolio position

    Args:
        symbol: The ticker symbol to retrieve position data for
    """
    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for position resource: {symbol}")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get portfolio data
        logger.info(f"Fetching portfolio data for symbol: {symbol}")
        portfolio = await ibkr_service.get_portfolio()

        # Find the specific position
        position = None
        for pos in portfolio:
            if pos.get("symbol", "").upper() == symbol.upper():
                position = pos
                break

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if position:
            # Format the position data
            formatted_position = {
                "symbol": position.get("symbol", ""),
                "position": position.get("position", 0),
                "marketPrice": position.get("marketPrice", 0),
                "marketValue": position.get("marketValue", 0),
                "averageCost": position.get("averageCost", 0),
                "unrealizedPNL": position.get("unrealizedPNL", 0),
                "realizedPNL": position.get("realizedPNL", 0)
            }

            return {
                "position": formatted_position,
                "timestamp": timestamp,
                "status": "success"
            }
        else:
            return {
                "error": f"Position not found for symbol: {symbol}",
                "errorType": "not_found",
                "status": "error",
                "timestamp": timestamp
            }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in position resource for {symbol}: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in position resource for {symbol}: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Position resource error for {symbol}: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Base account summary resource
@mcp.resource("ibkr://account-summary")
async def account_summary_resource() -> Dict:
    """
    Resource providing complete account summary data
    """
    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for account summary resource")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get account summary
        logger.info("Fetching account summary for resource")
        summary = await ibkr_service.get_account_summary()

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Return formatted data
        return {
            "accountSummary": summary,
            "timestamp": timestamp,
            "status": "success"
        }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in account summary resource: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in account summary resource: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Account summary resource error: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Parameterized account summary resource (specific value)
@mcp.resource("ibkr://account-summary/{tag}")
async def account_value_resource(tag: str) -> Dict:
    """
    Resource providing a specific account summary value

    Args:
        tag: The account summary tag to retrieve (e.g., "NetLiquidation", "BuyingPower")
    """
    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for account value resource: {tag}")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get account summary
        logger.info(f"Fetching account summary value for tag: {tag}")
        summary = await ibkr_service.get_account_summary()

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Find the specific value
        if tag in summary:
            return {
                "tag": tag,
                "value": summary[tag],
                "timestamp": timestamp,
                "status": "success"
            }
        else:
            return {
                "error": f"Account value not found for tag: {tag}",
                "errorType": "not_found",
                "status": "error",
                "timestamp": timestamp
            }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in account value resource for {tag}: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in account value resource for {tag}: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Account value resource error for {tag}: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Market data resource with symbol parameter
@mcp.resource("ibkr://market-data/{symbol}")
async def market_data_resource(symbol: str) -> Dict:
    """
    Resource providing market data for a specific symbol

    Args:
        symbol: The ticker symbol to retrieve market data for
    """
    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for market data resource: {symbol}")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get market data
        logger.info(f"Fetching market data for symbol: {symbol}")
        market_data = await ibkr_service.get_market_data(symbol)

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if market_data:
            return {
                "symbol": symbol,
                "data": market_data,
                "timestamp": timestamp,
                "status": "success"
            }
        else:
            return {
                "error": f"Market data not available for symbol: {symbol}",
                "errorType": "not_found",
                "status": "error",
                "timestamp": timestamp
            }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in market data resource for {symbol}: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in market data resource for {symbol}: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Market data resource error for {symbol}: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Advanced tool with progress reporting
@mcp.tool()
async def analyze_portfolio(risk_level: str = "medium", ctx: Context = None) -> Dict:
    """
    Analyze portfolio for risk and opportunities

    Args:
        risk_level: Risk tolerance level ("low", "medium", "high")
        ctx: MCP Context for progress reporting

    Returns:
        Portfolio analysis results
    """
    try:
        if not ibkr_service.connected:
            await ibkr_service.connect()

        # Get portfolio data
        if ctx:
            ctx.info("Fetching portfolio data...")
            await ctx.report_progress(0, 4)

        portfolio = await ibkr_service.get_portfolio()

        if ctx:
            ctx.info("Analyzing positions...")
            await ctx.report_progress(1, 4)

        # Simulate analysis (replace with actual analysis logic)
        await asyncio.sleep(1)

        if ctx:
            ctx.info("Calculating risk metrics...")
            await ctx.report_progress(2, 4)

        # Simulate risk calculation
        await asyncio.sleep(1)

        if ctx:
            ctx.info("Generating recommendations...")
            await ctx.report_progress(3, 4)

        # Generate recommendations based on risk level
        recommendations = []
        if risk_level == "low":
            recommendations = ["Consider diversifying with bonds", "Review stop losses"]
        elif risk_level == "medium":
            recommendations = ["Balance growth and value stocks", "Consider sector rotation"]
        else:  # high
            recommendations = ["Look for high-growth opportunities", "Consider options strategies"]

        if ctx:
            await ctx.report_progress(4, 4)

        return {
            "status": "success",
            "analysis": {
                "riskLevel": risk_level,
                "positionCount": len(portfolio),
                "recommendations": recommendations
            }
        }
    except Exception as e:
        logger.error(f"Portfolio analysis error: {str(e)}")
        return {"status": "error", "message": f"Failed to analyze portfolio: {str(e)}"}

# Connection management with proper error handling
@mcp.tool()
async def check_connection_status() -> Dict:
    """
    Check the current connection status to TWS/IB Gateway

    Returns:
        Connection status information
    """
    try:
        # Check if connected
        is_connected = ibkr_service.connected

        # Get additional connection details if available
        connection_time = getattr(ibkr_service, "connection_time", None)
        client_id = getattr(ibkr_service, "client_id", None)

        return {
            "status": "success",
            "connected": is_connected,
            "connectionTime": connection_time,
            "clientId": client_id,
            "host": getattr(ibkr_service, "host", None),
            "port": getattr(ibkr_service, "port", None)
        }
    except Exception as e:
        logger.error(f"Connection status error: {str(e)}")
        return {"status": "error", "message": f"Failed to check connection: {str(e)}"}

@mcp.tool()
async def disconnect_from_tws() -> Dict:
    """
    Disconnect from TWS/IB Gateway

    Returns:
        Disconnection status
    """
    try:
        # Disconnect using the service
        await ibkr_service.disconnect()

        return {
            "status": "success",
            "message": "Successfully disconnected from TWS/IB Gateway"
        }
    except Exception as e:
        logger.error(f"Disconnection error: {str(e)}")
        return {"status": "error", "message": f"Failed to disconnect: {str(e)}"}

# Server lifespan management
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(server: FastMCP):
    """
    Manage the server lifespan
    """
    # Startup
    logger.info("IBKR MCP Server starting up")

    # Initialize the service if needed
    # This doesn't connect to TWS yet - that happens when the user explicitly connects
    try:
        # Initialize the service
        await ibkr_service.initialize()
        logger.info("IBKR Service initialized")

        # Log server information
        logger.info(f"Server version: {server.version}")
        logger.info(f"Server name: {server.name}")
        logger.info(f"Server dependencies: {server.dependencies}")

        # Log environment information
        import platform
        logger.info(f"Python version: {platform.python_version()}")
        logger.info(f"Platform: {platform.platform()}")

    except Exception as e:
        logger.error(f"Failed to initialize IBKR Service: {str(e)}")
        # Don't re-raise the exception to allow the server to start anyway

    yield

    # Shutdown
    logger.info("IBKR MCP Server shutting down")

    # Disconnect from TWS if connected
    try:
        if ibkr_service.connected:
            logger.info("Disconnecting from TWS during shutdown")
            await ibkr_service.disconnect()
            logger.info("Successfully disconnected from TWS")
        else:
            logger.info("No active TWS connection to disconnect")

        # Perform any additional cleanup
        logger.info("Cleaning up resources")

        # Close any open matplotlib figures
        if MATPLOTLIB_AVAILABLE:
            plt.close('all')

        logger.info("Cleanup completed")
    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}")
    finally:
        logger.info("IBKR MCP Server shutdown complete")

# Set the lifespan manager
mcp.lifespan = lifespan

# Define startup and shutdown functions for compatibility with mcp_server_main.py
async def on_startup():
    """Compatibility function for mcp_server_main.py"""
    logger.info("on_startup called")
    # This is handled by the lifespan manager

async def on_shutdown():
    """Compatibility function for mcp_server_main.py"""
    logger.info("on_shutdown called")
    # This is handled by the lifespan manager

# Enhanced prompts for Claude Desktop
@mcp.prompt("ibkr://prompts/trading-assistant")
def trading_assistant_prompt() -> str:
    """
    Main prompt for the trading assistant
    """
    return """
    You are an Interactive Brokers trading assistant powered by Claude. You can help with:

    1. Connecting to TWS or IB Gateway
    2. Retrieving portfolio and account information
    3. Getting market data and searching for contracts
    4. Placing various types of orders (market, limit, stop, bracket, etc.)
    5. Analyzing portfolio risk and opportunities

    To get started, help the user connect to TWS using the connect_to_tws tool.
    For paper trading, use port 7497. For live trading, use port 7496.

    Always provide clear explanations of market data and portfolio information.
    When discussing orders, explain the order types and their implications.

    IMPORTANT: Always confirm before placing any orders, especially for large amounts.
    """

@mcp.prompt("ibkr://prompts/order-confirmation")
def order_confirmation_prompt(order_details: Dict) -> str:
    """
    Prompt for confirming order placement
    """
    symbol = order_details.get("symbol", "")
    action = order_details.get("action", "")
    quantity = order_details.get("quantity", 0)
    price = order_details.get("price", 0)
    order_type = order_details.get("orderType", "")

    return f"""
    Please confirm the following order details before I place the order:

    Symbol: {symbol}
    Action: {action}
    Quantity: {quantity}
    Price: ${price}
    Order Type: {order_type}

    Is this correct? Please respond with "Yes, place the order" to confirm,
    or "No, cancel" to cancel the order.
    """

@mcp.prompt("ibkr://prompts/market-analysis")
def market_analysis_prompt(symbol: str) -> str:
    """
    Prompt for market analysis
    """
    return f"""
    I'll analyze the market data for {symbol} and provide insights on:

    1. Current price and recent price action
    2. Volume analysis
    3. Technical indicators (if available)
    4. Potential support and resistance levels
    5. Overall market context

    Please note that this analysis is for informational purposes only and
    not financial advice.
    """

# Chart generation tool
@mcp.tool()
async def generate_price_chart(symbol: str, timeframe: str = "1d") -> Image:
    """
    Generate a price chart for a symbol

    Args:
        symbol: The ticker symbol
        timeframe: Timeframe (1d, 5d, 1m, 3m, 6m, 1y, 5y)

    Returns:
        An image of the price chart
    """
    if not symbol:
        logger.error("Symbol is required but not provided")
        return create_error_chart("Symbol is required")

    # Validate timeframe
    valid_timeframes = ["1d", "5d", "1m", "3m", "6m", "1y", "5y"]
    if timeframe not in valid_timeframes:
        logger.error(f"Invalid timeframe: {timeframe}")
        return create_error_chart(f"Invalid timeframe: {timeframe}. Valid options are: {', '.join(valid_timeframes)}")

    try:
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for chart generation: {symbol}")
            await ibkr_service.connect()
        else:
            # Verify connection is still valid
            await ibkr_service.check_connection()

        # Get historical data using the service
        logger.info(f"Fetching historical data for {symbol} with timeframe {timeframe}")
        historical_data = await ibkr_service.get_historical_data(symbol, timeframe)

        if not historical_data or len(historical_data) == 0:
            logger.warning(f"No historical data available for {symbol}")
            return create_error_chart(f"No historical data available for {symbol}")

        # Create a matplotlib figure
        plt.figure(figsize=(10, 6))
        plt.plot([bar['date'] for bar in historical_data],
                [bar['close'] for bar in historical_data])
        plt.title(f"{symbol} Price Chart ({timeframe})")
        plt.xlabel("Date")
        plt.ylabel("Price")
        plt.grid(True)

        # Add timestamp to the chart
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        plt.figtext(0.02, 0.02, f"Generated: {timestamp}", fontsize=8)

        # Save the figure to a bytes buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=100)
        buf.seek(0)
        plt.close()  # Close the figure to free memory

        # Create an MCP Image
        image_data = buf.getvalue()
        return Image(content=image_data, mime_type="image/png")

    except IBKRConnectionError as e:
        logger.error(f"Connection error while generating chart for {symbol}: {str(e)}")
        return create_error_chart(f"Connection error: {str(e)}")

    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while generating chart for {symbol}: {str(e)}")
        return create_error_chart(f"Timeout error: {str(e)}")

    except IBKRDataError as e:
        logger.error(f"Data error while generating chart for {symbol}: {str(e)}")
        return create_error_chart(f"Data error: {str(e)}")

    except Exception as e:
        logger.error(f"Unexpected error while generating chart for {symbol}: {str(e)}")
        return create_error_chart(f"Error generating chart: {str(e)}")

def create_error_chart(error_message: str) -> Image:
    """Create an error chart with the given message"""
    plt.figure(figsize=(10, 6))
    plt.text(0.5, 0.5, error_message,
            horizontalalignment='center', verticalalignment='center',
            fontsize=12, color='red')
    plt.axis('off')

    # Add timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    plt.figtext(0.02, 0.02, f"Error generated: {timestamp}", fontsize=8)

    buf = io.BytesIO()
    plt.savefig(buf, format='png', dpi=100)
    buf.seek(0)
    plt.close()  # Close the figure to free memory

    return Image(content=buf.getvalue(), mime_type="image/png")

# Custom error handling class
class IBKRError(Exception):
    """Custom exception for IBKR-related errors"""
    def __init__(self, message, error_code=None, request_id=None):
        self.message = message
        self.error_code = error_code
        self.request_id = request_id
        super().__init__(self.message)

# Enhanced error handling tool
@mcp.tool()
async def get_error_info(error_code: int) -> Dict:
    """
    Get information about a specific TWS API error code

    Args:
        error_code: The TWS API error code

    Returns:
        Information about the error
    """
    # TWS API error code dictionary
    error_codes = {
        100: "Max rate of messages per second has been exceeded",
        101: "Max number of tickers has been reached",
        102: "Duplicate ticker ID",
        103: "Duplicate order ID",
        104: "Can't modify a filled order",
        105: "Order being modified does not match original order",
        106: "Can't transmit order ID",
        107: "Can't transmit incomplete order",
        109: "Price is out of the acceptable range",
        110: "Price does not conform to the minimum price variation for this contract",
        111: "TIF type is not valid for this order",
        113: "The Locate field is blank",
        114: "The Locate field is invalid",
        115: "The security type is invalid",
        116: "The expiry field contains an invalid date",
        117: "The strike price field contains an invalid value",
        118: "The right field contains an invalid value",
        119: "The multiplier field contains an invalid value",
        120: "The symbol field contains an invalid value",
        121: "The exchange field contains an invalid value",
        122: "The primary exchange field contains an invalid value",
        123: "The currency field contains an invalid value",
        124: "The local symbol field contains an invalid value",
        125: "The trading class field contains an invalid value",
        126: "The including expired field contains an invalid value",
        129: "The security ID field contains an invalid value",
        131: "The security ID type field contains an invalid value",
        132: "The issue date field contains an invalid value",
        133: "The ratings group field contains an invalid value",
        134: "The ratings value field contains an invalid value",
        135: "The ratings rank field contains an invalid value",
        136: "The dividend schedule type field contains an invalid value",
        137: "The dividend schedule ID field contains an invalid value",
        140: "The delta field contains an invalid value",
        141: "The stock range lower field contains an invalid value",
        142: "The stock range upper field contains an invalid value",
        143: "The volatility field contains an invalid value",
        144: "The volatility type field contains an invalid value",
        145: "The reference price type field contains an invalid value",
        146: "The delta neutral order type field is not an allowed value",
        147: "The delta neutral aux price field contains an invalid value",
        148: "The basis points field contains an invalid value",
        149: "The basis points type field contains an invalid value",
        150: "The scale init level size field contains an invalid value",
        151: "The scale subs level size field contains an invalid value",
        152: "The scale price increment field contains an invalid value",
        153: "The hedge type field contains an invalid value",
        154: "The hedge param field contains an invalid value",
        155: "The account field contains an invalid value",
        156: "The settling firm field contains an invalid value",
        157: "The clearing account field contains an invalid value",
        158: "The clearing intent field contains an invalid value",
        159: "The allocation method field contains an invalid value",
        160: "The allocation percentage field contains an invalid value",
        161: "The allocation profile field contains an invalid value",
        162: "The dynamic allocation profile field contains an invalid value",
        163: "The allocation method specified is not allowed for the allocation profile",
        164: "The allocation profile specified does not exist",
        165: "The cash quantity field contains an invalid value",
        166: "The allocation profile cannot be used for the account",
        167: "The allocation profile violates the allocation method rules",
        168: "The allocation profile contains an invalid account",
        169: "The allocation profile contains an invalid amount",
        170: "The allocation profile contains a duplicate account",
        171: "The allocation profile contains an invalid allocation amount",
        172: "The allocation profile does not contain the account",
        173: "The allocation profile does not allocate the full amount",
        174: "The allocation profile contains an invalid allocation amount",
        175: "The soft dollar tier field contains an invalid value",
        176: "The soft dollar tier currency field contains an invalid value",
        177: "The soft dollar tier value field contains an invalid value",
        178: "The cash quantity currency field contains an invalid value",
        179: "The decision maker field contains an invalid value",
        180: "The decision maker type field contains an invalid value",
        181: "The min quantity field contains an invalid value",
        182: "The good after time field contains an invalid value",
        183: "The good till date field contains an invalid value",
        184: "The OCA group field contains an invalid value",
        185: "The OCA type field contains an invalid value",
        186: "The rule 80A field contains an invalid value",
        187: "The algo params field contains an invalid value",
        188: "The algo strategy field contains an invalid value",
        189: "The smart combo routing params field contains an invalid value",
        190: "The shares allocation profile field contains an invalid value",
        191: "The commission field contains an invalid value",
        192: "The commission currency field contains an invalid value",
        193: "The commission type field contains an invalid value",
        194: "The ratio field contains an invalid value",
        195: "The roll up value field contains an invalid value",
        196: "The roll up value format is invalid",
        197: "The variable rate reset field contains an invalid value",
        198: "The trailing amount field contains an invalid value",
        199: "The trailing percent field contains an invalid value",
        200: "The percent offset field contains an invalid value",
        201: "The scale price adjust value field contains an invalid value",
        202: "The scale price adjust interval field contains an invalid value",
        203: "The scale profit offset field contains an invalid value",
        204: "The scale auto reset field contains an invalid value",
        205: "The scale init position field contains an invalid value",
        206: "The scale init fill quantity field contains an invalid value",
        207: "The scale random percent field contains an invalid value",
        208: "The scale table field contains an invalid value",
        209: "The extended hours order field contains an invalid value",
        210: "The extended hours eligible field contains an invalid value",
        211: "The hidden field contains an invalid value",
        212: "The discretionary amount field contains an invalid value",
        213: "The shortable field contains an invalid value",
        214: "The locate field contains an invalid value",
        215: "The minimum trade quantity field contains an invalid value",
        216: "The minimum compress size field contains an invalid value",
        217: "The display size field contains an invalid value",
        218: "The block order field contains an invalid value",
        219: "The sweep to fill field contains an invalid value",
        220: "The all or none field contains an invalid value",
        221: "The outside regular trading hours field contains an invalid value",
        222: "The override constraints field contains an invalid value",
        223: "The hidden field is not available for this order type",
        224: "The override constraints field is not available for this order type",
        225: "The discretionary amount field is not available for this order type",
        226: "The transmit field contains an invalid value",
        227: "The designated location field contains an invalid value",
        228: "The delta neutral aux price field is not available for this order type",
        229: "The trailing amount field is not available for this order type",
        230: "The trailing percent field is not available for this order type",
        231: "The percent offset field is not available for this order type",
        232: "The shortable field is not available for this order type",
        233: "The start time field contains an invalid value",
        234: "The stock ref price field contains an invalid value",
        235: "The delta field is not available for this order type",
        236: "The stock range lower field is not available for this order type",
        237: "The stock range upper field is not available for this order type",
        238: "The volatility field is not available for this order type",
        239: "The volatility type field is not available for this order type",
        240: "The reference price type field is not available for this order type",
        241: "The delta neutral order type field is not available for this order type",
        242: "The continuous update field contains an invalid value",
        243: "The continuous update field is not available for this order type",
        244: "The reference contract id field contains an invalid value",
        245: "The reference contract id field is not available for this order type",
        246: "The pegged change amount field contains an invalid value",
        247: "The pegged change amount field is not available for this order type",
        248: "The reference change amount field contains an invalid value",
        249: "The reference change amount field is not available for this order type",
        250: "The basis points field is not available for this order type",
        251: "The basis points type field is not available for this order type",
        252: "The scale init level size field is not available for this order type",
        253: "The scale subs level size field is not available for this order type",
        254: "The scale price increment field is not available for this order type",
        256: "The scale price adjust value field is not available for this order type",
        257: "The scale price adjust interval field is not available for this order type",
        258: "The scale profit offset field is not available for this order type",
        259: "The scale auto reset field is not available for this order type",
        260: "The scale init position field is not available for this order type",
        261: "The scale init fill quantity field is not available for this order type",
        262: "The scale random percent field is not available for this order type",
        263: "The scale table field is not available for this order type",
        264: "The active start time field contains an invalid value",
        265: "The active start time field is not available for this order type",
        266: "The active stop time field contains an invalid value",
        267: "The active stop time field is not available for this order type",
        268: "The algo params field is not available for this order type",
        269: "The algo strategy field is not available for this order type",
        270: "The smart combo routing params field is not available for this order type",
        271: "The order combo legs field contains an invalid value",
        272: "The trailing amount field contains an invalid value",
        273: "The trailing percent field contains an invalid value",
        274: "The scale price increment amount field contains an invalid value",
        275: "The scale price increment amount field is not available for this order type",
        276: "The scale price increment origin field contains an invalid value",
        277: "The scale price increment origin field is not available for this order type",
        278: "The scale price increment origin is invalid for this order type",
        279: "The scale price increment origin is invalid for this scale price increment amount",
        280: "The scale price increment origin is invalid for this scale price increment amount",
        281: "The scale price increment origin is invalid for this scale price increment amount",
        282: "The scale price increment origin is invalid for this scale price increment amount",
        283: "The scale price increment origin is invalid for this scale price increment amount",
        284: "The scale price increment origin is invalid for this scale price increment amount",
        285: "The scale price increment origin is invalid for this scale price increment amount",
        286: "The scale price increment origin is invalid for this scale price increment amount",
        287: "The scale price increment origin is invalid for this scale price increment amount",
        288: "The scale price increment origin is invalid for this scale price increment amount",
        289: "The scale price increment origin is invalid for this scale price increment amount",
        290: "The scale price increment origin is invalid for this scale price increment amount",
        291: "The scale price increment origin is invalid for this scale price increment amount",
        292: "The scale price increment origin is invalid for this scale price increment amount",
        293: "The scale price increment origin is invalid for this scale price increment amount",
        294: "The scale price increment origin is invalid for this scale price increment amount",
        295: "The scale price increment origin is invalid for this scale price increment amount",
        296: "The scale price increment origin is invalid for this scale price increment amount",
        297: "The scale price increment origin is invalid for this scale price increment amount",
        298: "The scale price increment origin is invalid for this scale price increment amount",
        299: "The scale price increment origin is invalid for this scale price increment amount",
        300: "Can't find EId with ticker Id:",
        301: "Can't find ticker with EId:",
        302: "Can't find order with EId:",
        303: "Can't find account with EId:",
        304: "Can't modify this order as it is not in the system.",
        305: "The time in force specified is not valid.",
        306: "The order type specified is not valid.",
        307: "The order size is not valid.",
        308: "The account specified is not valid.",
        309: "The clearing account specified is not valid.",
        310: "The clearing intent specified is not valid.",
        311: "The outside regular trading hours specified is not valid.",
        312: "The sweep to fill specified is not valid.",
        313: "The all or none specified is not valid.",
        314: "The minimum quantity specified is not valid.",
        315: "The display size specified is not valid.",
        316: "The block order specified is not valid.",
        317: "The hidden order specified is not valid.",
        318: "The discretionary amount specified is not valid.",
        319: "The good after time specified is not valid.",
        320: "The good till date specified is not valid.",
        321: "The override constraints specified is not valid.",
        322: "The price specified is not valid.",
        323: "The trigger method specified is not valid.",
        324: "The volatility type specified is not valid.",
        325: "The reference price type specified is not valid.",
        326: "The continuous update specified is not valid.",
        327: "The delta neutral order type is not valid.",
        328: "The delta neutral aux price specified is not valid.",
        329: "The basis points specified is not valid.",
        330: "The basis points type specified is not valid.",
        331: "The hedge type specified is not valid.",
        332: "The hedge param specified is not valid.",
        333: "The OCA type specified is not valid.",
        334: "The transmit specified is not valid.",
        335: "The parent id specified is not valid.",
        336: "The short sale slot specified is not valid.",
        337: "The designated location specified is not valid.",
        338: "The exempt code specified is not valid.",
        339: "The audit trail specified is not valid.",
        340: "The start time specified is not valid.",
        341: "The stock ref price specified is not valid.",
        342: "The delta specified is not valid.",
        343: "The stock range lower specified is not valid.",
        344: "The stock range upper specified is not valid.",
        345: "The volatility specified is not valid.",
        346: "The volatility type specified is not valid.",
        347: "The reference price type specified is not valid.",
        348: "The delta neutral order type specified is not valid.",
        349: "The continuous update specified is not valid.",
        350: "The reference contract id specified is not valid.",
        351: "The pegged change amount specified is not valid.",
        352: "The reference change amount specified is not valid.",
        353: "The basis points specified is not valid.",
        354: "The basis points type specified is not valid.",
        355: "The scale init level size specified is not valid.",
        356: "The scale subs level size specified is not valid.",
        357: "The scale price increment specified is not valid.",
        358: "The scale price adjust value specified is not valid.",
        359: "The scale price adjust interval specified is not valid.",
        360: "The scale profit offset specified is not valid.",
        361: "The scale auto reset specified is not valid.",
        362: "The scale init position specified is not valid.",
        363: "The scale init fill qty specified is not valid.",
        364: "The scale random percent specified is not valid.",
        365: "The scale table specified is not valid.",
        366: "The active start time specified is not valid.",
        367: "The active stop time specified is not valid.",
        368: "The algo params specified is not valid.",
        369: "The algo strategy specified is not valid.",
        370: "The smart combo routing params specified is not valid.",
        371: "The order combo legs specified is not valid.",
        372: "The trailing amount specified is not valid.",
        373: "The trailing percent specified is not valid.",
        374: "The scale price increment amount specified is not valid.",
        375: "The scale price increment origin specified is not valid.",
        376: "The scale price increment origin is not valid for this scale price increment amount.",
        377: "The scale price increment origin is not valid for this scale price increment amount.",
        378: "The scale price increment origin is not valid for this scale price increment amount.",
        379: "The scale price increment origin is not valid for this scale price increment amount.",
        380: "The scale price increment origin is not valid for this scale price increment amount.",
        381: "The scale price increment origin is not valid for this scale price increment amount.",
        382: "The scale price increment origin is not valid for this scale price increment amount.",
        383: "The scale price increment origin is not valid for this scale price increment amount.",
        384: "The scale price increment origin is not valid for this scale price increment amount.",
        385: "The scale price increment origin is not valid for this scale price increment amount.",
        386: "The scale price increment origin is not valid for this scale price increment amount.",
        387: "The scale price increment origin is not valid for this scale price increment amount.",
        388: "The scale price increment origin is not valid for this scale price increment amount.",
        389: "The scale price increment origin is not valid for this scale price increment amount.",
        390: "The scale price increment origin is not valid for this scale price increment amount.",
        391: "The scale price increment origin is not valid for this scale price increment amount.",
        392: "The scale price increment origin is not valid for this scale price increment amount.",
        393: "The scale price increment origin is not valid for this scale price increment amount.",
        394: "The scale price increment origin is not valid for this scale price increment amount.",
        395: "The scale price increment origin is not valid for this scale price increment amount.",
        396: "The scale price increment origin is not valid for this scale price increment amount.",
        397: "The scale price increment origin is not valid for this scale price increment amount.",
        398: "The scale price increment origin is not valid for this scale price increment amount.",
        399: "The scale price increment origin is not valid for this scale price increment amount.",
        400: "The order is rejected.",
        401: "The order is canceled.",
        402: "The order is not canceled.",
        403: "The order is filled.",
        404: "The order is not filled.",
        405: "The order is partially filled.",
        406: "The order is not partially filled.",
        407: "The order is submitted.",
        408: "The order is not submitted.",
        409: "The order is not active.",
        410: "The order is active.",
        411: "The order is pending.",
        412: "The order is not pending.",
        413: "The order is pending cancel.",
        414: "The order is not pending cancel.",
        415: "The order is pending submit.",
        416: "The order is not pending submit.",
        417: "The order is pre-submitted.",
        418: "The order is not pre-submitted.",
        419: "The order is simulated.",
        420: "The order is not simulated.",
        421: "The order is not simulated.",
        422: "The order is not simulated.",
        423: "The order is not simulated.",
        424: "The order is not simulated.",
        425: "The order is not simulated.",
        426: "The order is not simulated.",
        427: "The order is not simulated.",
        428: "The order is not simulated.",
        429: "The order is not simulated.",
        430: "The order is not simulated.",
        431: "The order is not simulated.",
        432: "The order is not simulated.",
        433: "The order is not simulated.",
        434: "The order is not simulated.",
        435: "The order is not simulated.",
        436: "The order is not simulated.",
        437: "The order is not simulated.",
        438: "The order is not simulated.",
        439: "The order is not simulated.",
        440: "The order is not simulated.",
        441: "The order is not simulated.",
        442: "The order is not simulated.",
        443: "The order is not simulated.",
        444: "The order is not simulated.",
        445: "The order is not simulated.",
        446: "The order is not simulated.",
        447: "The order is not simulated.",
        448: "The order is not simulated.",
        449: "The order is not simulated.",
        500: "Unknown order type",
        501: "Unknown security type",
        502: "Unknown exchange",
        503: "Unknown currency",
        504: "Unknown trading class",
        505: "Unknown symbol",
        506: "Unknown expiry",
        507: "Unknown strike",
        508: "Unknown right",
        509: "Unknown multiplier",
        510: "Unknown local symbol",
        511: "Unknown security ID",
        512: "Unknown security ID type",
        513: "Unknown issue date",
        514: "Unknown ratings group",
        515: "Unknown ratings value",
        516: "Unknown ratings rank",
        517: "Unknown dividend schedule type",
        518: "Unknown dividend schedule ID",
        519: "Unknown delta neutral order type",
        520: "Unknown hedge type",
        521: "Unknown clearing account",
        522: "Unknown clearing intent",
        523: "Unknown allocation method",
        524: "Unknown allocation profile",
        525: "Unknown soft dollar tier",
        526: "Unknown cash quantity currency",
        527: "Unknown decision maker",
        528: "Unknown decision maker type",
        529: "Unknown OCA group",
        530: "Unknown OCA type",
        531: "Unknown rule 80A",
        532: "Unknown algo strategy",
        533: "Unknown algo params",
        534: "Unknown smart combo routing params",
        535: "Unknown shares allocation profile",
        536: "Unknown commission type",
        537: "Unknown commission currency",
        538: "Unknown reference contract ID",
        539: "Unknown scale table",
        540: "Unknown active start time",
        541: "Unknown active stop time",
        542: "Unknown min trade quantity",
        543: "Unknown min compress size",
        544: "Unknown display size",
        545: "Unknown block order",
        546: "Unknown sweep to fill",
        547: "Unknown all or none",
        548: "Unknown outside regular trading hours",
        549: "Unknown override constraints",
        550: "Unknown designated location",
        551: "Unknown delta neutral aux price",
        552: "Unknown trailing amount",
        553: "Unknown trailing percent",
        554: "Unknown percent offset",
        555: "Unknown scale price increment amount",
        556: "Unknown scale price increment origin",
        557: "Unknown scale price adjust value",
        558: "Unknown scale price adjust interval",
        559: "Unknown scale profit offset",
        560: "Unknown scale auto reset",
        561: "Unknown scale init position",
        562: "Unknown scale init fill quantity",
        563: "Unknown scale random percent",
        564: "Unknown hedge param",
        565: "Unknown volatility",
        566: "Unknown volatility type",
        567: "Unknown reference price type",
        568: "Unknown continuous update",
        569: "Unknown stock range lower",
        570: "Unknown stock range upper",
        571: "Unknown delta",
        572: "Unknown stock ref price",
        573: "Unknown basis points",
        574: "Unknown basis points type",
        575: "Unknown smart combo routing params",
        576: "Unknown order combo legs",
        577: "Unknown trailing amount",
        578: "Unknown trailing percent",
        10000: "TWS is not connected to the server",
        10001: "TWS is not running",
        10002: "TWS is not connected to IB server",
        10003: "TWS is not connected to IB server",
        10004: "TWS is not connected to IB server",
        10005: "TWS is not connected to IB server",
        10006: "TWS is not connected to IB server",
        10007: "TWS is not connected to IB server",
        10008: "TWS is not connected to IB server",
        10009: "TWS is not connected to IB server",
        10010: "TWS is not connected to IB server",
        10011: "TWS is not connected to IB server",
        10012: "TWS is not connected to IB server",
        10013: "TWS is not connected to IB server",
        10014: "TWS is not connected to IB server",
        10015: "TWS is not connected to IB server",
        10016: "TWS is not connected to IB server",
        10017: "TWS is not connected to IB server",
        10018: "TWS is not connected to IB server",
        10019: "TWS is not connected to IB server",
        10020: "TWS is not connected to IB server",
        10021: "TWS is not connected to IB server",
        10022: "TWS is not connected to IB server",
        10023: "TWS is not connected to IB server",
        10024: "TWS is not connected to IB server",
        10025: "TWS is not connected to IB server",
        10026: "TWS is not connected to IB server",
        10027: "TWS is not connected to IB server",
    }

    if error_code in error_codes:
        return {
            "status": "success",
            "error_code": error_code,
            "description": error_codes[error_code]
        }
    else:
        return {
            "status": "success",
            "error_code": error_code,
            "description": "Unknown error code"
        }

# Enhanced logging setup
def setup_logging():
    """
    Set up enhanced logging for the MCP server
    """
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Create logs directory in user's home directory
    import os
    from pathlib import Path
    
    # Use home directory for logs
    home_dir = Path.home()
    log_dir = home_dir / "ibkr_mcp_logs"
    
    # Create directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    
    # Full path to log file
    log_file = log_dir / "ibkr_mcp_server.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(str(log_file))
        ]
    )

    # Create a logger for this module
    global logger
    logger = logging.getLogger('ibkr_mcp')
    logger.setLevel(logging.INFO)

    # Log startup
    logger.info(f"IBKR MCP Server logging initialized (log file: {log_file})")

# Initialize logging
setup_logging()

# Main server configuration
if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="IBKR MCP Server")
    parser.add_argument("--host", default="127.0.0.1", help="TWS/IB Gateway host")
    parser.add_argument("--port", type=int, default=7497, help="TWS/IB Gateway port (7496 for live, 7497 for paper)")
    parser.add_argument("--client-id", type=int, default=1, help="Client ID for TWS connection")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--auto-connect", action="store_true", help="Automatically connect to TWS on startup")
    args = parser.parse_args()

    # Configure logging level based on debug flag
    if args.debug:
        logging.getLogger('ibkr_mcp').setLevel(logging.DEBUG)
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize the IBKR service with command line arguments
    ibkr_service = IBKRService(
        host=args.host,
        port=args.port,
        client_id=args.client_id
    )

    # Auto-connect if specified
    if args.auto_connect:
        asyncio.create_task(ibkr_service.connect())

    # Log server startup
    logging.info(f"Starting IBKR MCP Server (connecting to TWS at {args.host}:{args.port})")

    # Run the MCP server
    mcp.run()

# Documentation resources

# Base documentation resource
@mcp.resource("docs://overview")
def get_overview_docs() -> str:
    """Provide an overview of the IBKR MCP server"""
    return """
    # IBKR MCP Server

    This server provides access to Interactive Brokers' TWS API through the Model Context Protocol (MCP).

    ## Available Tools

    - **connect_to_tws**: Connect to TWS/IB Gateway
    - **disconnect_from_tws**: Disconnect from TWS/IB Gateway
    - **get_portfolio**: Get portfolio positions
    - **get_market_data**: Get real-time market data for a symbol
    - **place_order**: Place a new order
    - **cancel_order**: Cancel an existing order
    - **get_order_status**: Get the status of an order
    - **get_historical_data**: Get historical price data
    - **search_contracts**: Search for contracts by symbol
    - **generate_price_chart**: Generate a price chart for a symbol
    - **get_error_info**: Get information about TWS API error codes
    - **analyze_portfolio**: Analyze portfolio risk and opportunities
    - **check_connection_status**: Check the current connection status

    ## Available Resources

    - **ibkr://portfolio**: All portfolio positions
    - **ibkr://portfolio/{symbol}**: Specific portfolio position
    - **ibkr://account-summary**: Complete account summary
    - **ibkr://account-summary/{tag}**: Specific account value
    - **ibkr://market-data/{symbol}**: Market data for a specific symbol
    - **docs://overview**: This overview documentation
    - **docs://topic/{topic}**: Documentation on a specific topic
    - **docs://api/{section}**: API reference for a specific section

    ## Getting Started

    1. Start TWS or IB Gateway
    2. Use the `connect_to_tws` tool to establish a connection
    3. Use the available tools to interact with your IBKR account
    """

# Parameterized documentation resource
@mcp.resource("docs://topic/{topic}")
def get_topic_docs(topic: str) -> str:
    """
    Provide documentation on a specific topic

    Args:
        topic: The documentation topic to retrieve
    """
    topics = {
        "connection": """
        # Connection Management

        Before using the IBKR MCP server, you need to establish a connection to TWS or IB Gateway.

        ## Connecting to TWS

        Use the `connect_to_tws` tool to connect to TWS or IB Gateway:

        ```python
        result = await connect_to_tws(
            host="127.0.0.1",
            port=7497,  # 7497 for paper trading, 7496 for live trading
            client_id=0
        )
        ```

        ## Checking Connection Status

        Use the `check_connection_status` tool to check if you're connected:

        ```python
        status = await check_connection_status()
        ```

        ## Disconnecting from TWS

        Use the `disconnect_from_tws` tool to disconnect:

        ```python
        result = await disconnect_from_tws()
        ```
        """,

        "portfolio": """
        # Portfolio Management

        The IBKR MCP server provides tools and resources for accessing your portfolio data.

        ## Getting All Positions

        Use the `get_portfolio` tool to retrieve all positions:

        ```python
        portfolio = await get_portfolio()
        ```

        You can also access the portfolio resource:

        ```
        ibkr://portfolio
        ```

        ## Getting a Specific Position

        To get data for a specific position, use the parameterized resource:

        ```
        ibkr://portfolio/AAPL
        ```

        ## Analyzing Your Portfolio

        Use the `analyze_portfolio` tool to get risk analysis and recommendations:

        ```python
        analysis = await analyze_portfolio(risk_level="medium")
        ```
        """,

        "market-data": """
        # Market Data

        The IBKR MCP server provides tools for accessing market data.

        ## Getting Real-Time Market Data

        Use the `get_market_data` tool to get real-time market data:

        ```python
        data = await get_market_data(symbol="AAPL")
        ```

        You can also access the market data resource:

        ```
        ibkr://market-data/AAPL
        ```

        ## Getting Historical Data

        Use the `get_historical_data` tool to get historical price data:

        ```python
        data = await get_historical_data(symbol="AAPL", timeframe="1m")
        ```

        ## Generating Charts

        Use the `generate_price_chart` tool to create price charts:

        ```python
        chart = await generate_price_chart(symbol="AAPL", timeframe="1y")
        ```
        """,

        "trading": """
        # Trading

        The IBKR MCP server provides tools for placing and managing orders.

        ## Placing Orders

        Use the `place_order` tool to place new orders:

        ```python
        # Market order
        order_id = await place_order(
            symbol="AAPL",
            action="BUY",
            quantity=100,
            order_type="MKT"
        )

        # Limit order
        order_id = await place_order(
            symbol="MSFT",
            action="SELL",
            quantity=50,
            order_type="LMT",
            limit_price=350.00
        )
        ```

        ## Managing Orders

        Check order status:

        ```python
        status = await get_order_status(order_id=order_id)
        ```

        Cancel an order:

        ```python
        result = await cancel_order(order_id=order_id)
        ```
        """,

        "account": """
        # Account Information

        The IBKR MCP server provides tools and resources for accessing account information.

        ## Getting Account Summary

        Use the account summary resource to get all account values:

        ```
        ibkr://account-summary
        ```

        ## Getting Specific Account Values

        To get a specific account value, use the parameterized resource:

        ```
        ibkr://account-summary/NetLiquidation
        ibkr://account-summary/BuyingPower
        ibkr://account-summary/TotalCashValue
        ```

        Common account values include:
        - NetLiquidation: Net liquidation value
        - BuyingPower: Available buying power
        - TotalCashValue: Total cash value
        - EquityWithLoanValue: Equity with loan value
        - InitMarginReq: Initial margin requirement
        - MaintMarginReq: Maintenance margin requirement
        - AvailableFunds: Available funds
        - ExcessLiquidity: Excess liquidity
        """
    }

    # Return the requested topic or a list of available topics
    if topic.lower() in topics:
        return topics[topic.lower()]
    else:
        available_topics = ", ".join(topics.keys())
        return f"""
        # Documentation Topic Not Found

        The topic "{topic}" was not found. Available topics are:

        - {available_topics.replace(", ", "\n- ")}

        Access a topic using:
        ```
        docs://topic/{{topic_name}}
        ```
        """

# Parameterized API documentation resource
@mcp.resource("docs://api/{section}")
def get_api_section_docs(section: str) -> str:
    """
    Provide API documentation for a specific section

    Args:
        section: The API section to retrieve
    """
    sections = {
        "connection": """
        # Connection API

        ## connect_to_tws(host: str = "127.0.0.1", port: int = None, client_id: int = 0) -> Dict

        Connects to Interactive Brokers TWS or Gateway.

        **Parameters:**
        - `host`: The hostname or IP address of the TWS/Gateway (default: 127.0.0.1)
        - `port`: The port number (7496 for live trading, 7497 for paper trading)
        - `client_id`: A unique client ID (default: 0)

        **Returns:**
        - `Dict`: Connection status information

        ## disconnect_from_tws() -> Dict

        Disconnects from TWS/IB Gateway.

        **Returns:**
        - `Dict`: Disconnection status

        ## check_connection_status() -> Dict

        Checks the current connection status to TWS/IB Gateway.

        **Returns:**
        - `Dict`: Connection status information
        """,

        "portfolio": """
        # Portfolio API

        ## get_portfolio() -> Dict

        Gets the current portfolio positions.

        **Returns:**
        - `Dict`: Portfolio positions with details

        ## analyze_portfolio(risk_level: str = "medium") -> Dict

        Analyzes portfolio for risk and opportunities.

        **Parameters:**
        - `risk_level`: Risk tolerance level ("low", "medium", "high")

        **Returns:**
        - `Dict`: Portfolio analysis results
        """,

        "market-data": """
        # Market Data API

        ## get_market_data(symbol: str, exchange: str = "SMART") -> Dict

        Gets market data for a symbol.

        **Parameters:**
        - `symbol`: The ticker symbol
        - `exchange`: The exchange (default: SMART)

        **Returns:**
        - `Dict`: Market data including price, bid, ask, volume, etc.

        ## get_historical_data(symbol: str, timeframe: str = "1d") -> Dict

        Gets historical price data for a symbol.

        **Parameters:**
        - `symbol`: The ticker symbol
        - `timeframe`: Time period (1d, 5d, 1m, 3m, 6m, 1y, 5y)

        **Returns:**
        - `Dict`: Historical price data

        ## generate_price_chart(symbol: str, timeframe: str = "1d") -> Image

        Generates a price chart for a symbol.

        **Parameters:**
        - `symbol`: The ticker symbol
        - `timeframe`: Time period (1d, 5d, 1m, 3m, 6m, 1y, 5y)

        **Returns:**
        - `Image`: Chart image
        """,

        "trading": """
        # Trading API

        ## place_order(symbol: str, action: str, quantity: int, order_type: str, **kwargs) -> Dict

        Places a new order.

        **Parameters:**
        - `symbol`: The ticker symbol (e.g., "AAPL")
        - `action`: "BUY" or "SELL"
        - `quantity`: Number of shares/contracts
        - `order_type`: "MKT", "LMT", "STP", etc.
        - `limit_price`: Price for limit orders (optional)
        - `stop_price`: Price for stop orders (optional)
        - `security_type`: "STK", "OPT", "FUT", etc. (default: "STK")
        - `expiry`: Option expiry date in YYYYMMDD format (for options)
        - `strike`: Option strike price (for options)
        - `right`: "C" for call, "P" for put (for options)

        **Returns:**
        - `Dict`: Order confirmation with order ID

        ## cancel_order(order_id: int) -> Dict

        Cancels an existing order.

        **Parameters:**
        - `order_id`: The ID of the order to cancel

        **Returns:**
        - `Dict`: Cancellation status

        ## get_order_status(order_id: int) -> Dict

        Gets the status of an order.

        **Parameters:**
        - `order_id`: The ID of the order

        **Returns:**
        - `Dict`: Order status information
        """,

        "account": """
        # Account API

        ## get_account_summary() -> Dict

        Gets account summary information.

        **Returns:**
        - `Dict`: Account summary including cash balance, equity, margin, etc.
        """
    }

    # Return the requested section or a list of available sections
    if section.lower() in sections:
        return sections[section.lower()]
    else:
        available_sections = ", ".join(sections.keys())
        return f"""
        # API Section Not Found

        The API section "{section}" was not found. Available sections are:

        - {available_sections.replace(", ", "\n- ")}

        Access a section using:
        ```
        docs://api/{{section_name}}
        ```
        """
