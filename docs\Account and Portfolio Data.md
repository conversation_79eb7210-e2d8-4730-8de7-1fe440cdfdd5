# Account & Portfolio Data

## Managed Accounts

A single user name can handle more than one account. As mentioned in the [Connectivity](https://interactivebrokers.github.io/tws-api/connection.html) section, the TWS will automatically send a list of managed accounts once the connection is established. The list can also be fetched via the [IBApi.EClient.reqManagedAccts](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#af5dc6607457bc7bca764a49c10de0813) method:

```
self.reqManagedAccts()
```

The above will result in a comma separated list of account Ids delivered through [IBApi.EWrapper.managedAccounts](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#abd7e561f313bcc4c860074906199a46c)

```
class TestWrapper(wrapper.EWrapper):
...
		def managedAccounts(self, accountsList: str):
				super().managedAccounts(accountsList)
				print("Account list:", accountsList)
```

**Important:** whenever your TWS user name handles more than a single account, you will be forced to specify the account Id to which the order needs to be allocated. Failure to do so will result in the order being rejected since the TWS cannot assign the order to a default account.



## Account Updates

### Requesting

The [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) function creates a subscription to the TWS through which account and portfolio information is delivered. This information is the exact same as the one displayed within the TWS' Account Window. Note this function receives a specific account along with a flag indicating whether to start or stop the subscription. In a single account structure, the account number is not necessary. Just as with the TWS' Account Window, unless there is a position change this information is updated at a fixed interval of **three** minutes.

```
self.reqAccountUpdates(True, self.account)
```

### Receiving

Resulting account and portfolio information will be delivered via the [IBApi.EWrapper.updateAccountValue](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ae15a34084d9f26f279abd0bdeab1b9b5), [IBApi.EWrapper.updatePortfolio](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ac7b6423ee9522aeab2d2168b7d66c7b8), [IBApi.EWrapper.updateAccountTime](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a0f2fa798304a0cf101d57453f48c55f0) and [IBApi.EWrapper.accountDownloadEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a05f35c1d896eeee696487d483110354f)

```
class TestWrapper(wrapper.EWrapper):
...
		def updateAccountValue(self, key: str, val: str, currency: str,
													accountName: str):
				super().updateAccountValue(key, val, currency, accountName)
				print("UpdateAccountValue. Key:", key, "Value:", val,
							"Currency:", currency, "AccountName:", accountName)
...
		def updatePortfolio(self, contract: Contract, position: Decimal,
												marketPrice: float, marketValue: float,
												averageCost: float, unrealizedPNL: float,
												realizedPNL: float, accountName: str):
				super().updatePortfolio(contract, position, marketPrice, marketValue,
																averageCost, unrealizedPNL, realizedPNL, accountName)
				print("UpdatePortfolio.", "Symbol:", contract.symbol, "SecType:", contract.secType, "Exchange:",
							contract.exchange, "Position:", decimalMaxString(position), "MarketPrice:", floatMaxString(marketPrice),
							"MarketValue:", floatMaxString(marketValue), "AverageCost:", floatMaxString(averageCost),
							"UnrealizedPNL:", floatMaxString(unrealizedPNL), "RealizedPNL:", floatMaxString(realizedPNL),
							"AccountName:", accountName)
...
		def updateAccountTime(self, timeStamp: str):
				super().updateAccountTime(timeStamp)
				print("UpdateAccountTime. Time:", timeStamp)
...
		def accountDownloadEnd(self, accountName: str):
				super().accountDownloadEnd(accountName)
				print("AccountDownloadEnd. Account:", accountName)
```

### Cancelling

Once the subscription to account updates is no longer needed, it can be cancelled by invoking the [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) method while specifying the susbcription flag to be False:

```
self.reqAccountUpdates(False, self.account)
```

**Note:** An important key passed back in [IBApi.EWrapper.updateAccountValue](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ae15a34084d9f26f279abd0bdeab1b9b5) after a call to [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) is a boolean value 'accountReady'. If an accountReady value of false is returned that means that the IB server is in the process of resetting at that moment, i.e. the account is 'not ready'. When this occurs subsequent key values returned to [IBApi::EWrapper::updateAccountValue](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ae15a34084d9f26f279abd0bdeab1b9b5) in the current update can be out of date or incorrect.

**Important:** only one account at a time can be subscribed at a time. Attempting a second subscription without previously cancelling an active one will not yield any error message although it will override the already subscribed account with the new one. With Financial Advisory (FA) account structures there is an alternative way of specifying the account code such that information is returned for 'All' sub accounts- this is done by appending the letter 'A' to the end of the account number, i.e. reqAccountUpdates(true, "F123456A")

### Identifying the Account Keys

Account values delivered via [IBApi.EWrapper.updateAccountValue](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ae15a34084d9f26f279abd0bdeab1b9b5) can be classified in the following way:

- Commodities: suffixed by a "-C"
- Securities: suffixed by a "-S"
- Totals: no suffix

For further information, please refer to [the Account Window.](https://institutions.interactivebrokers.com/en/software/tws/usersguidebook/realtimeactivitymonitoring/the_account_window.htm)

### Account Value Update Subscriptions by Model

The [IBApi.EClient.reqAccountUpdatesMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a7233f67c6c69f3057994b6b97a366dfb) can be used in any account structure to create simultaneous account value subscriptions from one or multiple accounts and/or models. As with [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) the data returned will match that displayed within the TWS Account Window.

- To cancel a specific [IBApi.EClient.reqAccountUpdatesMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a7233f67c6c69f3057994b6b97a366dfb) subscription, the function [IBApi.EClient.cancelAccountUpdatesMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aff0d1003f82436d8bd1ba2e5a1cf09d5) is invoked with the same request ID used to make the original [IBApi.EClient.reqAccountUpdatesMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a7233f67c6c69f3057994b6b97a366dfb) subscription request.

  

- *[IBApi.EClient.reqAccountUpdatesMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a7233f67c6c69f3057994b6b97a366dfb) cannot be used with Account="All" in IBroker accounts with more than 50 subaccounts

- If using TWS **v983+** a profile name can be accepted in place of group in the **account** parameter. See [Unification of Groups and Profiles](https://interactivebrokers.github.io/tws-api/financial_advisor.html#groups_merge)

```
self.reqAccountUpdatesMulti(9005, self.account, "", True)
```

The resulting account and portfolio information will be delivered via the [IBApi.EWrapper.accountUpdateMulti](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ae9114b5146bb8f32796f9b9d21569d7c) and [IBApi.EWrapper.accountUpdateMultiEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#aeb5dd139597e26edd978b300ccd0450b)

```
class TestWrapper(wrapper.EWrapper):
...
		def accountUpdateMulti(self, reqId: int, account: str, modelCode: str,
													key: str, value: str, currency: str):
				super().accountUpdateMulti(reqId, account, modelCode, key, value,
																	currency)
				print("AccountUpdateMulti. RequestId:", reqId, "Account:", account,
							"ModelCode:", modelCode, "Key:", key, "Value:", value,
							"Currency:", currency)
...
		def accountUpdateMultiEnd(self, reqId: int):
				super().accountUpdateMultiEnd(reqId)
				print("AccountUpdateMultiEnd. RequestId:", reqId)
```

## Account Summary

### Requesting

The [IBApi.EClient.reqAccountSummary](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a3e0d55d36cd416639b97ee6e47a86fe9) method creates a subscription for the account data displayed in the TWS Account Summary window. It is commonly used with multiple-account structures. Introducing broker (IBroker) accounts with more than 50 subaccounts or configured for on-demand account lookup cannot use reqAccountSummary with group="All". If using TWS **v983+** a profile name can be accepted in place of group. See [Unification of Groups and Profiles](https://interactivebrokers.github.io/tws-api/financial_advisor.html#groups_merge)

Unlike [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f), [IBApi.EClient.reqAccountSummary](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a3e0d55d36cd416639b97ee6e47a86fe9) can not only retrieve summarized information for either one or all the managed accounts but also extract only the specified values to be monitored by the client application. The initial invocation of reqAccountSummary will result in a list of all requested values being returned, and then every three minutes those values which have changed will be returned. The update frequency of 3 minutes is the same as the TWS Account Window and cannot be changed.

```
self.reqAccountSummary(9001, "All", AccountSummaryTags.AllTags)
```

Starting from TWS Build 956 and IB Gateway 956, we have added the function to request account summary data (including *CashBalance* and *TotalCashBalance*) for every currency separately using **LEDGER** tags. Please see [TWS Beta Release Notes](https://www.interactivebrokers.com/en/index.php?f=1656&ns=T&nhf=T).

When the "$LEDGER" tag is specified, the account summary data will be returned in BASE CURRENCY only.

```
self.reqAccountSummary(9002, "All", "$LEDGER")
```

When the "$LEDGER:CURRENCY" tag is specified, the account summary data will be returned only in the CURRENCY specified. The CashBalance and TotalCashBalance returned are the balance in that specific currency only as you see within the TWS Account Window.

Example: "$LEDGER:USD", "$LEDGER:EUR", "$LEDGER:HKD" etc.

```
self.reqAccountSummary(9003, "All", "$LEDGER:EUR")
```

When the "$LEDGER:ALL" tag is specified, the account summary data returned will be summed up values for ALL accounts and currencies.

Example:

Account = All, Currency = EUR, CashBalance = 12345.67

Account = All, Currency = JPY, CashBalance = 987.54

```
self.reqAccountSummary(9004, "All", "$LEDGER:ALL")
```

**Important:** only **two** active summary subscriptions are allowed at a time!

### Receiving

Summarised information is delivered via [IBApi.EWrapper.accountSummary](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#acd761f48771f61dd0fb9e9a7d88d4f04) and [IBApi.EWrapper.accountSummaryEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a12bf8483858526077140c950e80f2995)

```
class TestWrapper(wrapper.EWrapper):
...
		def accountSummary(self, reqId: int, account: str, tag: str, value: str,
											currency: str):
				super().accountSummary(reqId, account, tag, value, currency)
				print("AccountSummary. ReqId:", reqId, "Account:", account,
							"Tag: ", tag, "Value:", value, "Currency:", currency)
...
		def accountSummaryEnd(self, reqId: int):
				super().accountSummaryEnd(reqId)
				print("AccountSummaryEnd. ReqId:", reqId)
```

### Cancelling

Once the subscription to account summary is no longer needed, it can be cancelled via the [IBApi::EClient::cancelAccountSummary](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#af5ec205466b47cdab2cd17931b529e82) method:

```
self.cancelAccountSummary(9001)
self.cancelAccountSummary(9002)
self.cancelAccountSummary(9003)
self.cancelAccountSummary(9004)
```

## Positions

### Requesting

A limitation of the function [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f) is that it can only be used with a single account at a time. To create a subscription for position updates from multiple accounts, the function [IBApi.EClient.reqPositions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ab262cf5601e57d6612d3df5e821fca9e) is available.

Note: The reqPositions function is not available in Introducing Broker or Financial Advisor master accounts that have very large numbers of subaccounts (> 50) to optimize the performance of TWS/IB Gateway v973+. Instead the function reqPositionsMulti can be used to subscribe to updates from individual subaccounts. Also not available with IBroker accounts configured for on-demand account lookup.

After initially invoking reqPositions, information about all positions in all associated accounts will be returned, followed by the [IBApi::EWrapper::positionEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#acf1bebfc1b29cbeff32da7d53aec0971) callback. Thereafter, when a position has changed an update will be returned to the [IBApi::EWrapper::position](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a399a086e671468ea7ff4e80a48051b46) function. To cancel a reqPositions subscription, invoke [IBApi::EClient::cancelPositions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ab2159a39733d8967928317524875aa62).

```
self.reqPositions()
```

### Receiving

After invoking the above, the positions will then be received through the [IBApi.EWrapper.position](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a399a086e671468ea7ff4e80a48051b46) callback. After the **initial callback** (only) of all positions, the [IBApi.EWrapper.positionEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#acf1bebfc1b29cbeff32da7d53aec0971) function will be triggered.

- For futures, the exchange field will not be populated in the position callback as some futures trade on multiple exchanges

```
class TestWrapper(wrapper.EWrapper):
...
		def position(self, account: str, contract: Contract, position: Decimal,
								avgCost: float):
				super().position(account, contract, position, avgCost)
				print("Position.", "Account:", account, "Symbol:", contract.symbol, "SecType:",
							contract.secType, "Currency:", contract.currency,
							"Position:", decimalMaxString(position), "Avg cost:", floatMaxString(avgCost))
...
		def positionEnd(self):
				super().positionEnd()
				print("PositionEnd")
```

### Cancelling

To cancel the reqPosition subscription, invoke [IBApi::EClient::cancelPositions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ab2159a39733d8967928317524875aa62):

```
self.cancelPositions()
```

### Position Update Subscription by Model

The function [IBApi.EClient.reqPositionsMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a4fa2744c3459f9f6cf695980267608c3) can be used with any account structure to subscribe to positions updates for multiple accounts and/or models. The account and model parameters are optional if there are not multiple accounts or models available. It is more efficient to use this function for a specific subset of accounts than using [IBApi.EClient.reqPositions](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ab262cf5601e57d6612d3df5e821fca9e). If using TWS **v983+** a profile name can be accepted in place of group in the **account** parameter. See [Unification of Groups and Profiles](https://interactivebrokers.github.io/tws-api/financial_advisor.html#groups_merge)

```
self.reqPositionsMulti(9006, self.account, "")
```

After invoking [IBApi.EClient.reqPositionsMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a4fa2744c3459f9f6cf695980267608c3) data will be returned to the [IBApi.EWrapper.positionMulti](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a4d2e4fd47efe2212b6fee85a8abde0f2) function. After the initial callback of all positions matching the supplied criteria to reqPositionsMulti, the [IBApi.EWrapper.positionMultiEnd](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a46c77101413adc69812d808b907cff9d) function will be triggered. Thereafter, there will only be messages sent to positionsMulti when there is a change. To cancel a positionsMulti subscription the function [IBApi.EClient.cancelPositionsMulti](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ae919658c15bceba6b6cf2a1336d0acbf) is invoked with the same request ID used to create the subscription.

```
class TestWrapper(wrapper.EWrapper):
...
		def positionMulti(self, reqId: int, account: str, modelCode: str,
											contract: Contract, pos: Decimal, avgCost: float):
				super().positionMulti(reqId, account, modelCode, contract, pos, avgCost)
				print("PositionMulti. RequestId:", reqId, "Account:", account,
							"ModelCode:", modelCode, "Symbol:", contract.symbol, "SecType:",
							contract.secType, "Currency:", contract.currency, ",Position:",
							decimalMaxString(pos), "AvgCost:", floatMaxString(avgCost))
...
		def positionMultiEnd(self, reqId: int):
				super().positionMultiEnd(reqId)
				print("PositionMultiEnd. RequestId:", reqId)
```

## Profit And Loss (P&L)

### P&L data in the Account Window

UnRealized and Realized P&L is sent to the API function [IBApi.EWrapper.updateAccountValue](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ae15a34084d9f26f279abd0bdeab1b9b5) function after a subscription request is made with [IBApi.EClient.reqAccountUpdates](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aea1b0d9b6b85a4e0b18caf13a51f837f). This information corresponds to the data in the TWS Account Window, and has a different source of information, a different update frequency, and different reset schedule than PnL data in the TWS Portfolio Window and associated API functions (below). In particular, the unrealized P&L information shown in the TWS Account Window which is sent to updatePortfolioValue will update either **(1)** when a trade for that particular instrument occurs or **(2)** every 3 minutes. The realized P&L data in the TWS [Account Window](https://www.interactivebrokers.com/en/software/tws/usersguidebook/realtimeactivitymonitoring/the_account_window.htm) is reset to 0 once per day.

- It is important to keep in mind that the P&L data shown in the Account Window and Portfolio Window will sometimes differ because there is a different source of information and a different reset schedule.

### P&L data in the Portfolio Window

Beginning with API v973.03, requests can be made to receive real time updates about the daily P&L and unrealized P&L for an account, or for individual positions. Financial Advisors can also request P&L figures for 'All' subaccounts, or for a portfolio model. In API v973.05+/TWS v968+ this is further extended to include realized P&L information at the account or individual position level.

These newer P&L API functions demonstrated below return the data which is displayed in the TWS [Portfolio Window](https://www.interactivebrokers.com/en/software/tws/usersguidebook/realtimeactivitymonitoring/profitloss.htm) in current versions of TWS (v963+). As such, the P&L values are calculated based on the reset schedule specified in TWS Global Configuration (by default an instrument-specific reset schedule) and this setting affects values sent to the associated API functions as well. Also in TWS, P&L data from virtual forex positions will be included in the account P&L if and only if the Virtual Fx section of the Account Window is expanded.

- **Note:** the P&L functions in Python API are available starting in API v973.06+.

#### P&L subscription requests for individual positions

Subscribe using the [IBApi::EClient::reqPnLSingle](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a9ba0dc47f80ff9e836a235a0dea791b3) function Cannot be used with IBroker accounts configured for on-demand lookup with account = 'All'

```
self.reqPnLSingle(17002, "DU111519", "", 8314)
```

Currently updates are returned to [IBApi.EWrapper.pnlSingle](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a0e59ff22fa7ce2e6d5e4222346a5d706) approximately once per second. *subject to change in the future

```
def pnlSingle(self, reqId: int, pos: Decimal, dailyPnL: float,
							unrealizedPnL: float, realizedPnL: float, value: float):
		super().pnlSingle(reqId, pos, dailyPnL, unrealizedPnL, realizedPnL, value)
		print("Daily PnL Single. ReqId:", reqId, "Position:", decimalMaxString(pos),
					"DailyPnL:", floatMaxString(dailyPnL), "UnrealizedPnL:", floatMaxString(unrealizedPnL),
					"RealizedPnL:", floatMaxString(realizedPnL), "Value:", floatMaxString(value))
```



- If a P&L subscription request is made for an invalid conId or contract not in the account, there will not be a response.
- As elsewhere in the API, a max double value will indicate an 'unset' value. This corresponds to an empty cell in TWS.
- Introducing broker accounts without a large number of subaccounts (<50) can receive aggregate data by specifying the account as "All".
- *Cannot be used with IBroker accounts configured for on-demand lookup with account = 'All'

Subscriptions are cancelled using the [IBApi::EClient::cancelPnLSingle](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#accd383725ef440070775654a9ab4bf47) function:

```
self.cancelPnLSingle(17002)
```

#### P&L subscription requests for accounts

Subscribe using the [IBApi::EClient::reqPnL](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a0351f22a77b5ba0c0243122baf72fa45) function:

```
self.reqPnL(17001, "DU111519", "")
```

- Introducing broker accounts with less than 50 subaccounts can receive aggregate PnL for all subaccounts by specifying 'All' as the account code.
- With requests for advisor accounts with many subaccounts and/or positions can take several seconds for aggregated P&L to be computed and returned.
- For account P&L data the TWS setting "Prepare portfolio PnL data when downloading positions" must be checked.

Updates are sent to [IBApi.EWrapper.pnl](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#ad19e3db5eaded96b4ed7aefe886d9d59)

```
def pnl(self, reqId: int, dailyPnL: float,
				unrealizedPnL: float, realizedPnL: float):
		super().pnl(reqId, dailyPnL, unrealizedPnL, realizedPnL)
		print("Daily PnL. ReqId:", reqId, "DailyPnL:", floatMaxString(dailyPnL),
					"UnrealizedPnL:", floatMaxString(unrealizedPnL), "RealizedPnL:", floatMaxString(realizedPnL))
```

Cancel unnecessary subscriptions with the [IBApi::EClient::cancelPnL](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#a5a805731fedd8f40130a51a459328572) function:

```
self.cancelPnL(17001)
```

