{"mcpServers": {"ibkr": {"name": "IBKR MCP Server", "description": "Interactive Brokers trading capabilities", "command": "C:\\Projects\\ib-tws\\ibkr_mcp_server\\venv\\Scripts\\python.exe", "args": ["mcp_server_main.py"], "cwd": "C:\\Projects\\ib-tws", "env": {"PYTHONPATH": "C:\\Projects\\ib-tws", "PYTHONUNBUFFERED": "1", "IBKR_HOST": "127.0.0.1", "IBKR_PORT": "7497", "IBKR_CLIENT_ID": "0"}, "transport": {"type": "stdio"}}, "filesystem": {"name": "Official Filesystem Server", "description": "Secure file operations with configurable access controls", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Projects\\TV_alert_trader\\source_code"], "transport": {"type": "stdio"}}, "fetch": {"name": "Official Fetch Server", "description": "Web content fetching and conversion for efficient LLM usage", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts\\uvx.exe", "args": ["mcp-server-fetch"], "transport": {"type": "stdio"}}, "memory": {"name": "Memory Server", "description": "Knowledge graph-based persistent memory system", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "transport": {"type": "stdio"}}, "git": {"name": "Git Server", "description": "Git repository operations and management", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "C:\\Projects\\ib-tws"], "transport": {"type": "stdio"}}}}