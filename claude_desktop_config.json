{"name": "IBKR Trading Assistant", "description": "Interactive Brokers trading assistant for <PERSON>", "version": "1.0.0", "servers": [{"name": "IBKR MCP Server", "description": "Provides access to Interactive Brokers TWS API", "transport": {"type": "stdio", "command": "python3", "args": ["/Users/<USER>/projects/b-team/mcp_server_main.py"], "env": {"IBKR_HOST": "127.0.0.1", "IBKR_PORT": "7497", "IBKR_CLIENT_ID": "0"}}, "variables": [{"name": "IBKR_HOST", "description": "TWS/Gateway hostname or IP address", "defaultValue": "127.0.0.1"}, {"name": "IBKR_PORT", "description": "TWS/Gateway port (7496 for live, 7497 for paper)", "defaultValue": "7497"}, {"name": "IBKR_CLIENT_ID", "description": "Client ID for TWS/Gateway connection", "defaultValue": "0"}]}]}